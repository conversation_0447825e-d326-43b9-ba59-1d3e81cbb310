#!/usr/bin/env python3
"""
Dashboard Interativo da Carteira de Investimentos

Este script executa o dashboard Streamlit para análise temporal da carteira.
Para executar: streamlit run dashboard_carteira.py
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Importar e executar o dashboard
from analise_carteira_temporal import criar_dashboard

if __name__ == "__main__":
    criar_dashboard()
