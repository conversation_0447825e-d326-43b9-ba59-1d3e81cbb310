# 📊 Ambiente de Análise Financeira

Este projeto está configurado com `uv` para análises financeiras usando Python.

## 🚀 Bibliotecas Instaladas

- **yfinance**: Para obter dados financeiros do Yahoo Finance
- **pandas**: Para manipulação e análise de dados
- **numpy**: Para computação numérica
- **matplotlib**: Para criação de gráficos
- **seaborn**: Para visualizações estatísticas avançadas
- **scipy**: Para computação científica e estatística

## 📋 Como Usar

### Executar o teste do ambiente:
```bash
uv run python main.py
```

### Executar análises:
```bash
# Exemplo geral de análise
uv run python exemplo_analise.py

# ⭐ GRÁFICOS DE AÇÕES BRASILEIRAS:

# 1. Gráficos simples (preço + volume):
uv run python gerar_graficos_simples.py     # 20 principais ações ✅ EXECUTADO
uv run python todas_acoes_73.py             # Todas as 73 ações

# 2. Gráficos com MÉDIAS MÓVEIS (200 dias):
uv run python graficos_com_medias_moveis.py # 20 principais ações ✅ EXECUTADO
uv run python todas_acoes_73_com_mm.py      # Todas as 73 ações com MM

# 3. ANÁLISE DE CORRELAÇÃO:
uv run python correlacao_acoes.py           # Correlação + CSV ✅ EXECUTADO

# Outros scripts:
uv run python acoes_brasileiras.py          # Análise interativa
uv run python graficos_rapidos.py           # Gráficos rápidos
uv run python exemplo_uso.py                # Tutorial
```

### Instalar novas bibliotecas:
```bash
uv add nome_da_biblioteca
```

### Executar scripts Python:
```bash
uv run python seu_script.py
```

### Ativar o ambiente virtual (opcional):
```bash
source .venv/bin/activate
```

## 📈 Scripts Disponíveis

### `exemplo_analise.py`
Exemplo completo de análise financeira com funções para:
- Obter dados históricos de ações
- Calcular métricas financeiras básicas
- Criar gráficos de preços e volume
- Comparar performance de múltiplas ações

### `acoes_brasileiras.py` 🇧🇷
Script especializado para análise de ações brasileiras:
- **73 ações brasileiras** pré-configuradas
- Gráficos comparativos e individuais
- Ranking de performance do último ano
- Análise completa com TOP 10

### `graficos_rapidos.py` ⚡
Script para gráficos rápidos das principais ações:
- **15 principais ações** brasileiras
- Comparação por setores (Bancos, Commodities, Consumo, Infraestrutura)
- Resumo de performance
- Interface simples e rápida

### `correlacao_acoes.py` 🔍 ✅ EXECUTADO
Script para análise de correlação entre ações:
- **Mapa de calor** de correlações entre 46 ações
- Identificação de **624 pares** com baixa correlação (≤0.3)
- **TOP 10 ações para diversificação** exportadas em CSV
- Gráficos de análise estatística
- **Arquivos CSV gerados**: `acoes_diversificacao.csv`, `pares_baixa_correlacao.csv`

## 🔧 Estrutura do Projeto

```
finance/
├── main.py                        # Teste do ambiente
├── exemplo_analise.py             # Exemplo de análise financeira
├── acoes_brasileiras.py           # Análise interativa de ações
├── graficos_rapidos.py            # Gráficos rápidos
├── gerar_graficos_simples.py      # ✅ Gráficos 20 ações (executado)
├── todas_acoes_73.py              # Gráficos todas as 73 ações
├── graficos_com_medias_moveis.py  # ✅ Gráficos com MM (executado)
├── todas_acoes_73_com_mm.py       # Todas as 73 ações com MM
├── correlacao_acoes.py            # ✅ Análise correlação (executado)
├── exemplo_uso.py                 # Tutorial
├── pyproject.toml                 # Configuração do projeto
├── uv.lock                       # Lock file das dependências
└── README.md                     # Este arquivo
```

## 💡 Dicas

- Use `uv run` para executar scripts sem ativar o ambiente virtual
- O `uv` gerencia automaticamente as dependências e o ambiente virtual
- Para análises interativas, considere usar Jupyter notebooks: `uv add jupyter`