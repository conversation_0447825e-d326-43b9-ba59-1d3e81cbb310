# Modificações para Features Individuais de OHLC

## Resumo das Alterações

As features dos estimadores XGBoost e LightGBM foram modificadas para usar os valores individuais de OHLC (Open, High, Low, Close) ao invés da média OHLC. O valor a ser estimado (target) continua sendo o mesmo.

## Arquivos Modificados

### 1. `src/features_xgboost.py`
- **Adicionado**: Cálculo de `pct_change` individual para cada componente OHLC
- **Novas features**: `Open_PctChange`, `High_PctChange`, `Low_PctChange`, `Close_PctChange`
- **Novas features lagged**: Para cada lag de 1 a `ohlc_lags`:
  - `Open_PctChange_Lag_{i}`
  - `High_PctChange_Lag_{i}`
  - `Low_PctChange_Lag_{i}`
  - `Close_PctChange_Lag_{i}`
- **Mantido**: Features da média OHLC para compatibilidade

### 2. `src/estimador_xgboost_sinais.py`
- **Modificado**: Lista de features básicas para incluir features individuais de OHLC
- **Adicionado**: 4 features por lag (Open, High, Low, Close) ao invés de 1 (média)
- **Total de features OHLC**: `4 * ohlc_lags` features individuais + `ohlc_lags` features da média

### 3. `src/estimador_lightgbm_sinais.py`
- **Modificado**: Lista de features básicas para incluir features individuais de OHLC
- **Estrutura**: Mesma lógica do XGBoost para consistência
- **Target**: Continua sendo a variação percentual da média OHLC

### 4. `src/classificador_xgboost_sinais.py`
- **Modificado**: Lista de features básicas para incluir features individuais de OHLC
- **Consistência**: Mesmas features dos estimadores para compatibilidade

### 5. `src/estimador_lstm_sinais.py`
- **Modificado**: Lista de features básicas para incluir features individuais de OHLC
- **Compatibilidade**: Mantém consistência com outros modelos

### 6. `src/predicao_sinais_xgboost.py`
- **Modificado**: Função `preparar_features()` para calcular features individuais de OHLC
- **Adicionado**: Cálculo e lags das features individuais
- **Preenchimento**: NaN preenchidos com zero para evitar problemas

## Estrutura das Novas Features

### Features Básicas (por componente OHLC)
```python
# Para cada componente (Open, High, Low, Close):
dados[f'{component}_PctChange'] = dados[component].pct_change()

# Para cada lag de 1 a ohlc_lags:
dados[f'{component}_PctChange_Lag_{i}'] = dados[f'{component}_PctChange'].shift(i)
```

### Exemplo com ohlc_lags = 5
**Antes (apenas média OHLC):**
- `Media_OHLC_PctChange_Lag_1`
- `Media_OHLC_PctChange_Lag_2`
- `Media_OHLC_PctChange_Lag_3`
- `Media_OHLC_PctChange_Lag_4`
- `Media_OHLC_PctChange_Lag_5`

**Depois (componentes individuais + média):**
- `Open_PctChange_Lag_1` até `Open_PctChange_Lag_5`
- `High_PctChange_Lag_1` até `High_PctChange_Lag_5`
- `Low_PctChange_Lag_1` até `Low_PctChange_Lag_5`
- `Close_PctChange_Lag_1` até `Close_PctChange_Lag_5`
- `Media_OHLC_PctChange_Lag_1` até `Media_OHLC_PctChange_Lag_5` (mantido para compatibilidade)

## Impacto no Número de Features

- **Antes**: `ohlc_lags` features (apenas média OHLC)
- **Depois**: `5 * ohlc_lags` features (4 componentes individuais + média)
- **Exemplo com ohlc_lags = 5**: 5 features → 25 features OHLC

## Target (Valor a ser Estimado)

**Não foi alterado**: Continua sendo a variação percentual da média OHLC em relação ao dia anterior:
```python
Target_PctChange = (Media_OHLC_hoje - Media_OHLC_ontem) / Media_OHLC_ontem * 100
```

## Benefícios Esperados

1. **Maior granularidade**: Modelos podem capturar padrões específicos de cada componente OHLC
2. **Informação adicional**: Diferenças entre Open/Close e High/Low podem ser relevantes
3. **Flexibilidade**: Modelos podem aprender pesos diferentes para cada componente
4. **Compatibilidade**: Features antigas mantidas para comparação

## Teste de Validação

Executado `test_ohlc_features.py` com sucesso:
- ✅ 40 features individuais de OHLC criadas corretamente
- ✅ Valores válidos em todas as features
- ✅ Estatísticas consistentes entre componentes individuais e média

## Próximos Passos

1. Executar treinamento dos modelos com as novas features
2. Comparar performance com versão anterior
3. Analisar importância das features individuais vs média
4. Considerar remoção das features da média se não agregarem valor
