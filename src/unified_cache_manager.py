#!/usr/bin/env python3
"""
Gerenciador do Cache Unificado
Todas as ações em um único arquivo para máxima eficiência
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import time

# Adicionar o diretório src ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import setup_environment
from cache_unified import unified_cache

def show_cache_status():
    """Mostra status do cache unificado"""
    print("📊 Status do Cache Unificado:")
    print("=" * 50)
    
    stats = unified_cache.get_cache_stats()
    
    if not stats['exists']:
        print("📁 Nenhum cache unificado encontrado")
        return
    
    print(f"📦 Arquivo: {stats['file_size_mb']:.1f}MB")
    print(f"📊 Total de registros: {stats['total_records']:,}")
    print(f"🎯 Total de ações: {stats['total_tickers']}")
    print(f"📅 Última atualização: {stats['last_update']}")
    print(f"🗜️ Compressão: {stats['compression']}")
    
    if stats['date_range']['start'] and stats['date_range']['end']:
        start_date = pd.to_datetime(stats['date_range']['start']).strftime('%d/%m/%Y')
        end_date = pd.to_datetime(stats['date_range']['end']).strftime('%d/%m/%Y')
        print(f"📅 Período: {start_date} até {end_date}")
    
    # Comparar com caches individuais
    print(f"\n📈 Comparação com caches individuais:")
    
    # Cache CSV individual
    csv_cache_dir = Path('data/cache/historical_data')
    if csv_cache_dir.exists():
        csv_files = list(csv_cache_dir.glob('*_historical.csv'))
        csv_total_size = sum(f.stat().st_size for f in csv_files)
        print(f"   📁 Cache CSV individual: {len(csv_files)} arquivos, {csv_total_size/1024/1024:.1f}MB")
        
        if csv_total_size > 0:
            efficiency = (1 - stats['file_size'] / csv_total_size) * 100
            print(f"   🎯 Eficiência do unificado: {efficiency:.1f}% menor")
    
    # Cache Parquet individual
    parquet_cache_dir = Path('data/cache/optimized')
    if parquet_cache_dir.exists():
        parquet_files = list(parquet_cache_dir.glob('*.parquet'))
        parquet_total_size = sum(f.stat().st_size for f in parquet_files)
        print(f"   📦 Cache Parquet individual: {len(parquet_files)} arquivos, {parquet_total_size/1024/1024:.1f}MB")
        
        if parquet_total_size > 0:
            efficiency = (1 - stats['file_size'] / parquet_total_size) * 100
            print(f"   🎯 Eficiência do unificado: {efficiency:.1f}% menor")

def benchmark_unified_vs_individual():
    """Compara performance entre cache unificado e individual"""
    print("🚀 Benchmark: Cache Unificado vs Individual")
    print("=" * 60)
    
    # Carregar lista de ações para teste
    try:
        acoes_df = pd.read_csv('acoes_diversificacao.csv')
        test_tickers = [(row['Ticker'] + '.SA', row['Nome']) for _, row in acoes_df.head(10).iterrows()]
    except:
        # Fallback para ações conhecidas
        test_tickers = [
            ('PETR4.SA', 'Petrobras'),
            ('VALE3.SA', 'Vale'),
            ('BBAS3.SA', 'Banco do Brasil'),
            ('WEGE3.SA', 'WEG'),
            ('ABEV3.SA', 'Ambev')
        ]
    
    print(f"🎯 Testando com {len(test_tickers)} ações...")
    
    # Teste 1: Cache Unificado
    print("\n1️⃣ Testando Cache Unificado:")
    start_time = time.time()
    
    unified_data = unified_cache.update_unified_cache(test_tickers)
    if unified_data is not None:
        # Extrair dados de cada ação
        extracted_count = 0
        for ticker, nome in test_tickers:
            ticker_data = unified_cache.get_ticker_data(unified_data, ticker)
            if ticker_data is not None:
                extracted_count += 1
        
        unified_time = time.time() - start_time
        print(f"   ⏱️ Tempo total: {unified_time:.2f}s")
        print(f"   ✅ Ações processadas: {extracted_count}/{len(test_tickers)}")
        print(f"   📊 Registros totais: {len(unified_data):,}")
    else:
        print("   ❌ Falha no cache unificado")
        unified_time = float('inf')
    
    # Teste 2: Cache Individual (se disponível)
    print("\n2️⃣ Testando Cache Individual:")
    
    try:
        from cache_optimized import optimized_cache
        
        start_time = time.time()
        individual_count = 0
        
        for ticker, nome in test_tickers:
            data, info = optimized_cache.get_cached_data(ticker)
            if data is not None:
                individual_count += 1
        
        individual_time = time.time() - start_time
        print(f"   ⏱️ Tempo total: {individual_time:.2f}s")
        print(f"   ✅ Ações processadas: {individual_count}/{len(test_tickers)}")
        
        # Comparação
        if unified_time != float('inf') and individual_time > 0:
            speedup = individual_time / unified_time
            print(f"\n🏆 Resultado:")
            print(f"   🚀 Cache Unificado: {unified_time:.2f}s")
            print(f"   📁 Cache Individual: {individual_time:.2f}s")
            print(f"   ⚡ Speedup: {speedup:.1f}x mais rápido")
        
    except ImportError:
        print("   ⚠️ Cache individual não disponível")

def migrate_to_unified():
    """Migra caches existentes para o formato unificado"""
    print("🔄 Migrando para Cache Unificado...")
    print("=" * 50)
    
    # Verificar se há caches existentes para migrar
    csv_cache_dir = Path('data/cache/historical_data')
    parquet_cache_dir = Path('data/cache/optimized')
    
    tickers_to_migrate = []
    
    # Coletar tickers do cache CSV
    if csv_cache_dir.exists():
        csv_files = list(csv_cache_dir.glob('*_historical.csv'))
        for csv_file in csv_files:
            ticker_clean = csv_file.stem.replace('_historical', '')
            ticker = f"{ticker_clean}.SA"
            tickers_to_migrate.append((ticker, ticker_clean))
        print(f"📁 Encontrados {len(csv_files)} arquivos CSV")
    
    # Coletar tickers do cache Parquet individual
    if parquet_cache_dir.exists():
        parquet_files = list(parquet_cache_dir.glob('*.parquet'))
        for parquet_file in parquet_files:
            ticker_clean = parquet_file.stem
            ticker = f"{ticker_clean}.SA"
            if (ticker, ticker_clean) not in tickers_to_migrate:
                tickers_to_migrate.append((ticker, ticker_clean))
        print(f"📦 Encontrados {len(parquet_files)} arquivos Parquet individuais")
    
    if not tickers_to_migrate:
        print("📁 Nenhum cache existente encontrado para migrar")
        return
    
    print(f"🎯 Migrando {len(tickers_to_migrate)} ações para cache unificado...")
    
    # Criar cache unificado
    start_time = time.time()
    unified_data = unified_cache.update_unified_cache(tickers_to_migrate)
    
    if unified_data is not None:
        migration_time = time.time() - start_time
        stats = unified_cache.get_cache_stats()
        
        print(f"✅ Migração concluída em {migration_time:.2f}s")
        print(f"📦 Cache unificado: {stats['file_size_mb']:.1f}MB")
        print(f"📊 Total de registros: {stats['total_records']:,}")
        print(f"🎯 Ações migradas: {stats['total_tickers']}")
        
        # Calcular economia de espaço
        original_size = 0
        if csv_cache_dir.exists():
            original_size += sum(f.stat().st_size for f in csv_cache_dir.glob('*_historical.csv'))
        if parquet_cache_dir.exists():
            original_size += sum(f.stat().st_size for f in parquet_cache_dir.glob('*.parquet'))
        
        if original_size > 0:
            space_saved = (original_size - stats['file_size']) / original_size * 100
            print(f"💾 Economia de espaço: {space_saved:.1f}%")
    else:
        print("❌ Falha na migração")

def clear_unified_cache():
    """Limpa o cache unificado"""
    unified_cache.clear_cache()

def main():
    """Função principal"""
    setup_environment()
    
    if len(sys.argv) < 2:
        print("🔧 Gerenciador de Cache Unificado")
        print("=" * 40)
        print("Opções disponíveis:")
        print("  status      - Mostrar status do cache unificado")
        print("  benchmark   - Comparar performance unificado vs individual")
        print("  migrate     - Migrar caches existentes para formato unificado")
        print("  clear       - Limpar cache unificado")
        print("  help        - Mostrar esta ajuda")
        print()
        print("Uso: python unified_cache_manager.py [opção]")
        print()
        print("💡 Vantagens do Cache Unificado:")
        print("  • Download em lote (muito mais rápido)")
        print("  • Arquivo único (mais eficiente)")
        print("  • Menor uso de espaço em disco")
        print("  • Melhor performance de I/O")
        return
    
    opcao = sys.argv[1].lower()
    
    if opcao == "status":
        show_cache_status()
    elif opcao == "benchmark":
        benchmark_unified_vs_individual()
    elif opcao == "migrate":
        migrate_to_unified()
    elif opcao == "clear":
        clear_unified_cache()
    elif opcao == "help":
        main()
    else:
        print(f"❌ Opção '{opcao}' não reconhecida")
        main()

if __name__ == "__main__":
    main()
