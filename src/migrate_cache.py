#!/usr/bin/env python3
"""
Script para migrar cache CSV existente para formato Parquet otimizado
"""

import os
import sys
import pandas as pd
from pathlib import Path
import time

# Adicionar o diretório src ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import setup_environment
from cache_optimized import optimized_cache

def migrate_csv_to_parquet():
    """
    Migra arquivos de cache CSV para formato Parquet
    """
    setup_environment()
    
    csv_cache_dir = Path('data/cache/historical_data')
    
    if not csv_cache_dir.exists():
        print("📁 Nenhum cache CSV encontrado para migrar")
        return
    
    csv_files = list(csv_cache_dir.glob('*_historical.csv'))
    
    if not csv_files:
        print("📁 Nenhum arquivo CSV de cache encontrado")
        return
    
    print(f"🔄 Migrando {len(csv_files)} arquivos de cache CSV para Parquet...")
    print("=" * 60)
    
    migrated = 0
    errors = 0
    total_csv_size = 0
    total_parquet_size = 0
    
    for csv_file in csv_files:
        try:
            # Extrair ticker do nome do arquivo
            ticker_clean = csv_file.stem.replace('_historical', '')
            ticker = f"{ticker_clean}.SA"
            
            print(f"📊 Migrando {ticker_clean}...")
            
            # Ler arquivo CSV
            start_time = time.time()
            data = pd.read_csv(csv_file, index_col=0, parse_dates=True)
            csv_size = csv_file.stat().st_size
            total_csv_size += csv_size
            
            # Salvar em formato Parquet
            optimized_cache.save_cached_data(ticker, data, source="migrated_from_csv")
            
            # Calcular tamanho do arquivo Parquet
            parquet_path = optimized_cache._get_cache_path(ticker)
            parquet_size = parquet_path.stat().st_size
            total_parquet_size += parquet_size
            
            # Calcular estatísticas
            compression_ratio = (csv_size - parquet_size) / csv_size * 100
            load_time = time.time() - start_time
            
            print(f"   ✅ {ticker_clean}: {csv_size/1024:.1f}KB → {parquet_size/1024:.1f}KB "
                  f"({compression_ratio:.1f}% redução, {load_time:.2f}s)")
            
            migrated += 1
            
        except Exception as e:
            print(f"   ❌ Erro ao migrar {csv_file.name}: {e}")
            errors += 1
    
    print("=" * 60)
    print(f"📊 Migração concluída:")
    print(f"   ✅ Migrados: {migrated} arquivos")
    print(f"   ❌ Erros: {errors} arquivos")
    print(f"   📦 Tamanho CSV total: {total_csv_size/1024/1024:.1f}MB")
    print(f"   📦 Tamanho Parquet total: {total_parquet_size/1024/1024:.1f}MB")
    
    if total_csv_size > 0:
        total_compression = (total_csv_size - total_parquet_size) / total_csv_size * 100
        print(f"   🎯 Compressão total: {total_compression:.1f}%")
        print(f"   💾 Espaço economizado: {(total_csv_size - total_parquet_size)/1024/1024:.1f}MB")

def compare_performance():
    """
    Compara performance de leitura entre CSV e Parquet
    """
    print("\n🚀 Comparando performance de leitura...")
    print("=" * 50)
    
    csv_cache_dir = Path('data/cache/historical_data')
    csv_files = list(csv_cache_dir.glob('*_historical.csv'))[:5]  # Testar apenas 5 arquivos
    
    if not csv_files:
        print("📁 Nenhum arquivo CSV encontrado para comparação")
        return
    
    total_csv_time = 0
    total_parquet_time = 0
    
    for csv_file in csv_files:
        ticker_clean = csv_file.stem.replace('_historical', '')
        ticker = f"{ticker_clean}.SA"
        
        # Testar leitura CSV
        start_time = time.time()
        csv_data = pd.read_csv(csv_file, index_col=0, parse_dates=True)
        csv_time = time.time() - start_time
        total_csv_time += csv_time
        
        # Testar leitura Parquet
        start_time = time.time()
        parquet_data, _ = optimized_cache.get_cached_data(ticker)
        parquet_time = time.time() - start_time
        total_parquet_time += parquet_time
        
        if parquet_data is not None:
            speedup = csv_time / parquet_time if parquet_time > 0 else 0
            print(f"   {ticker_clean}: CSV {csv_time:.3f}s vs Parquet {parquet_time:.3f}s "
                  f"(speedup: {speedup:.1f}x)")
    
    if total_parquet_time > 0:
        overall_speedup = total_csv_time / total_parquet_time
        print(f"\n🎯 Speedup médio: {overall_speedup:.1f}x")
        print(f"   📊 Tempo total CSV: {total_csv_time:.3f}s")
        print(f"   📊 Tempo total Parquet: {total_parquet_time:.3f}s")

def show_cache_comparison():
    """
    Mostra comparação entre os dois sistemas de cache
    """
    print("\n📊 Comparação dos sistemas de cache:")
    print("=" * 50)
    
    # Cache CSV
    csv_cache_dir = Path('data/cache/historical_data')
    if csv_cache_dir.exists():
        csv_files = list(csv_cache_dir.glob('*_historical.csv'))
        csv_total_size = sum(f.stat().st_size for f in csv_files)
        print(f"📁 Cache CSV:")
        print(f"   Arquivos: {len(csv_files)}")
        print(f"   Tamanho total: {csv_total_size/1024/1024:.1f}MB")
    else:
        print(f"📁 Cache CSV: Não encontrado")
    
    # Cache Parquet
    stats = optimized_cache.get_cache_stats()
    print(f"📦 Cache Parquet:")
    print(f"   Arquivos: {stats['total_files']}")
    if 'total_size_mb' in stats:
        print(f"   Tamanho total: {stats['total_size_mb']:.1f}MB")
        print(f"   Compressão: {stats['compression']}")
    else:
        print(f"   Tamanho total: 0.0MB")
        print(f"   Status: Nenhum cache Parquet encontrado")

def main():
    """
    Função principal
    """
    if len(sys.argv) < 2:
        print("🔧 Migrador de Cache CSV → Parquet")
        print("=" * 40)
        print("Opções disponíveis:")
        print("  migrate     - Migrar cache CSV para Parquet")
        print("  compare     - Comparar performance CSV vs Parquet")
        print("  status      - Mostrar status dos caches")
        print("  help        - Mostrar esta ajuda")
        print()
        print("Uso: python migrate_cache.py [opção]")
        return
    
    opcao = sys.argv[1].lower()
    
    if opcao == "migrate":
        migrate_csv_to_parquet()
    elif opcao == "compare":
        compare_performance()
    elif opcao == "status":
        show_cache_comparison()
    elif opcao == "help":
        main()
    else:
        print(f"❌ Opção '{opcao}' não reconhecida")
        main()

if __name__ == "__main__":
    main()
