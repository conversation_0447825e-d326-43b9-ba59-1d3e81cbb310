#!/usr/bin/env python3
"""
Monitor de Performance para o sistema de cache otimizado
Monitora uso de CPU, memória e performance de I/O
"""

import os
import sys
import time
import psutil
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import json

# Adicionar o diretório src ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import setup_environment
from cache_optimized import optimized_cache

class PerformanceMonitor:
    """
    Monitor de performance do sistema
    """
    
    def __init__(self):
        self.stats_file = Path('data/cache/performance_stats.json')
        self.stats_file.parent.mkdir(parents=True, exist_ok=True)
        
    def get_system_stats(self):
        """Coleta estatísticas do sistema"""
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_available_gb': psutil.virtual_memory().available / (1024**3),
            'disk_usage_percent': psutil.disk_usage('/').percent,
            'disk_free_gb': psutil.disk_usage('/').free / (1024**3)
        }
    
    def benchmark_cache_operations(self, num_operations=10):
        """
        Benchmark das operações de cache
        """
        print(f"🚀 Executando benchmark com {num_operations} operações...")
        
        # Selecionar algumas ações para teste
        test_tickers = ['PETR4.SA', 'VALE3.SA', 'BBAS3.SA', 'WEGE3.SA', 'ABEV3.SA'][:num_operations]
        
        results = {
            'read_times': [],
            'write_times': [],
            'file_sizes': [],
            'memory_usage': []
        }
        
        for ticker in test_tickers:
            # Monitorar uso de memória antes
            mem_before = psutil.virtual_memory().percent
            
            # Benchmark de leitura
            start_time = time.time()
            data, info = optimized_cache.get_cached_data(ticker)
            read_time = time.time() - start_time
            
            if data is not None:
                results['read_times'].append(read_time)
                results['file_sizes'].append(info.get('file_size', 0))
                
                # Benchmark de escrita (re-salvar os mesmos dados)
                start_time = time.time()
                optimized_cache.save_cached_data(ticker, data, source="benchmark")
                write_time = time.time() - start_time
                results['write_times'].append(write_time)
                
                # Monitorar uso de memória depois
                mem_after = psutil.virtual_memory().percent
                results['memory_usage'].append(mem_after - mem_before)
                
                print(f"   {ticker.replace('.SA', '')}: "
                      f"Read {read_time:.3f}s, Write {write_time:.3f}s, "
                      f"Size {info.get('file_size', 0)/1024:.1f}KB")
        
        # Calcular estatísticas
        if results['read_times']:
            stats = {
                'avg_read_time': sum(results['read_times']) / len(results['read_times']),
                'avg_write_time': sum(results['write_times']) / len(results['write_times']),
                'avg_file_size': sum(results['file_sizes']) / len(results['file_sizes']),
                'total_memory_impact': sum(results['memory_usage']),
                'operations_tested': len(results['read_times'])
            }
            
            print(f"\n📊 Resultados do Benchmark:")
            print(f"   📖 Tempo médio de leitura: {stats['avg_read_time']:.3f}s")
            print(f"   💾 Tempo médio de escrita: {stats['avg_write_time']:.3f}s")
            print(f"   📦 Tamanho médio de arquivo: {stats['avg_file_size']/1024:.1f}KB")
            print(f"   🧠 Impacto na memória: {stats['total_memory_impact']:.1f}%")
            
            return stats
        
        return None
    
    def analyze_cache_efficiency(self):
        """
        Analisa a eficiência do cache
        """
        print("📊 Analisando eficiência do cache...")
        
        cache_stats = optimized_cache.get_cache_stats()
        
        if cache_stats['total_files'] == 0:
            print("❌ Nenhum cache encontrado para análise")
            return
        
        # Calcular estatísticas de compressão
        csv_cache_dir = Path('data/cache/historical_data')
        if csv_cache_dir.exists():
            csv_files = list(csv_cache_dir.glob('*_historical.csv'))
            csv_total_size = sum(f.stat().st_size for f in csv_files)
            
            compression_ratio = (csv_total_size - cache_stats['total_size']) / csv_total_size * 100
            space_saved = (csv_total_size - cache_stats['total_size']) / (1024**2)
            
            print(f"💾 Eficiência de Armazenamento:")
            print(f"   CSV total: {csv_total_size/1024/1024:.1f}MB")
            print(f"   Parquet total: {cache_stats['total_size_mb']:.1f}MB")
            print(f"   Compressão: {compression_ratio:.1f}%")
            print(f"   Espaço economizado: {space_saved:.1f}MB")
        
        # Analisar distribuição de tamanhos
        parquet_dir = Path('data/cache/optimized')
        if parquet_dir.exists():
            file_sizes = [f.stat().st_size for f in parquet_dir.glob('*.parquet')]
            if file_sizes:
                print(f"📈 Distribuição de Tamanhos:")
                print(f"   Menor arquivo: {min(file_sizes)/1024:.1f}KB")
                print(f"   Maior arquivo: {max(file_sizes)/1024:.1f}KB")
                print(f"   Tamanho médio: {sum(file_sizes)/len(file_sizes)/1024:.1f}KB")
    
    def save_performance_log(self, stats):
        """Salva log de performance"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'system_stats': self.get_system_stats(),
            'cache_stats': optimized_cache.get_cache_stats(),
            'benchmark_results': stats
        }
        
        # Carregar logs existentes
        logs = []
        if self.stats_file.exists():
            try:
                with open(self.stats_file, 'r') as f:
                    logs = json.load(f)
            except:
                logs = []
        
        # Adicionar novo log
        logs.append(log_entry)
        
        # Manter apenas últimos 100 logs
        logs = logs[-100:]
        
        # Salvar
        with open(self.stats_file, 'w') as f:
            json.dump(logs, f, indent=2, default=str)
        
        print(f"📝 Log de performance salvo: {self.stats_file}")
    
    def show_performance_history(self, days=7):
        """Mostra histórico de performance"""
        if not self.stats_file.exists():
            print("📁 Nenhum histórico de performance encontrado")
            return
        
        try:
            with open(self.stats_file, 'r') as f:
                logs = json.load(f)
        except:
            print("❌ Erro ao ler histórico de performance")
            return
        
        # Filtrar logs dos últimos dias
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_logs = [
            log for log in logs 
            if datetime.fromisoformat(log['timestamp']) > cutoff_date
        ]
        
        if not recent_logs:
            print(f"📁 Nenhum log encontrado nos últimos {days} dias")
            return
        
        print(f"📊 Histórico de Performance (últimos {days} dias):")
        print("=" * 60)
        
        for log in recent_logs[-10:]:  # Mostrar últimos 10
            timestamp = datetime.fromisoformat(log['timestamp'])
            sys_stats = log['system_stats']
            
            print(f"🕐 {timestamp.strftime('%d/%m/%Y %H:%M:%S')}")
            print(f"   CPU: {sys_stats['cpu_percent']:.1f}% | "
                  f"RAM: {sys_stats['memory_percent']:.1f}% | "
                  f"Disco: {sys_stats['disk_usage_percent']:.1f}%")
            
            if 'benchmark_results' in log and log['benchmark_results']:
                bench = log['benchmark_results']
                print(f"   Cache: Read {bench['avg_read_time']:.3f}s | "
                      f"Write {bench['avg_write_time']:.3f}s")
            print()

def main():
    """Função principal"""
    setup_environment()
    
    if len(sys.argv) < 2:
        print("🔧 Monitor de Performance - Sistema de Cache")
        print("=" * 50)
        print("Opções disponíveis:")
        print("  benchmark   - Executar benchmark de performance")
        print("  analyze     - Analisar eficiência do cache")
        print("  history     - Mostrar histórico de performance")
        print("  system      - Mostrar estatísticas do sistema")
        print("  help        - Mostrar esta ajuda")
        print()
        print("Uso: python performance_monitor.py [opção]")
        return
    
    monitor = PerformanceMonitor()
    opcao = sys.argv[1].lower()
    
    if opcao == "benchmark":
        stats = monitor.benchmark_cache_operations()
        if stats:
            monitor.save_performance_log(stats)
    elif opcao == "analyze":
        monitor.analyze_cache_efficiency()
    elif opcao == "history":
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
        monitor.show_performance_history(days)
    elif opcao == "system":
        stats = monitor.get_system_stats()
        print("💻 Estatísticas do Sistema:")
        print(f"   CPU: {stats['cpu_percent']:.1f}%")
        print(f"   Memória: {stats['memory_percent']:.1f}% "
              f"({stats['memory_available_gb']:.1f}GB disponível)")
        print(f"   Disco: {stats['disk_usage_percent']:.1f}% "
              f"({stats['disk_free_gb']:.1f}GB livre)")
    elif opcao == "help":
        main()
    else:
        print(f"❌ Opção '{opcao}' não reconhecida")
        main()

if __name__ == "__main__":
    main()
