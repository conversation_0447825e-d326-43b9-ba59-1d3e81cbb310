#!/usr/bin/env python3
"""
Gerenciador de Cache para dados históricos do XGBoost
Permite verificar status, limpar cache e configurar opções de cache
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

def verificar_status_cache():
    """
    Verifica o status do cache histórico
    """
    cache_dir = 'data/cache/historical_data'
    if not os.path.exists(cache_dir):
        print("📁 Nenhum cache encontrado")
        return
    
    arquivos_cache = [f for f in os.listdir(cache_dir) if f.endswith('_historical.csv')]
    if not arquivos_cache:
        print("📁 Diretório de cache vazio")
        return
    
    print(f"📊 Status do cache histórico ({len(arquivos_cache)} ações):")
    hoje = datetime.now().date()
    
    # Configurações
    max_days_outdated = config.get('xgboost.cache.max_days_outdated', 3)
    
    atualizados = 0
    desatualizados = 0
    com_erro = 0
    
    for arquivo in sorted(arquivos_cache):
        ticker = arquivo.replace('_historical.csv', '')
        cache_file = os.path.join(cache_dir, arquivo)
        
        try:
            dados = pd.read_csv(cache_file, index_col=0, parse_dates=True)
            ultima_data = dados.index.max().date()
            dias_desatualizados = (hoje - ultima_data).days
            
            if dias_desatualizados == 0:
                status = "✅ Atualizado"
                atualizados += 1
            elif dias_desatualizados <= max_days_outdated:
                status = f"🟡 {dias_desatualizados} dia(s) atrás"
                atualizados += 1
            else:
                status = f"🔴 {dias_desatualizados} dias atrás"
                desatualizados += 1
                
            print(f"   {ticker}: {len(dados)} dias, última: {ultima_data} - {status}")
        except Exception as e:
            print(f"   {ticker}: ❌ Erro ao ler cache - {e}")
            com_erro += 1
    
    print(f"\n📈 Resumo:")
    print(f"   ✅ Atualizados: {atualizados}")
    print(f"   🔴 Desatualizados: {desatualizados}")
    print(f"   ❌ Com erro: {com_erro}")
    print(f"   📊 Total: {len(arquivos_cache)}")

def limpar_cache_historico():
    """
    Remove todos os arquivos de cache histórico
    """
    cache_dir = 'data/cache/historical_data'
    if os.path.exists(cache_dir):
        import shutil
        arquivos_removidos = len([f for f in os.listdir(cache_dir) if f.endswith('_historical.csv')])
        shutil.rmtree(cache_dir)
        print(f"🗑️ Cache histórico limpo - {arquivos_removidos} arquivos removidos")
    else:
        print("📁 Nenhum cache encontrado para limpar")

def mostrar_configuracoes_cache():
    """
    Mostra as configurações atuais de cache
    """
    print("⚙️ Configurações de cache atuais:")
    print(f"   use_cache: {config.get('xgboost.cache.use_cache', True)}")
    print(f"   force_download: {config.get('xgboost.cache.force_download', False)}")
    print(f"   max_days_outdated: {config.get('xgboost.cache.max_days_outdated', 3)}")
    print(f"   data_period: {config.get('xgboost.data_period', '15y')}")

def mostrar_ajuda():
    """
    Mostra opções disponíveis
    """
    print("🔧 Gerenciador de Cache - XGBoost")
    print("=" * 50)
    print("Opções disponíveis:")
    print("  status    - Verificar status do cache")
    print("  clear     - Limpar cache histórico")
    print("  config    - Mostrar configurações atuais")
    print("  help      - Mostrar esta ajuda")
    print()
    print("Uso: python cache_manager.py [opção]")
    print()
    print("💡 Dicas:")
    print("  • Configure as opções no arquivo config.yaml")
    print("  • use_cache: true/false - habilita/desabilita cache")
    print("  • force_download: true/false - força download completo")
    print("  • max_days_outdated: número de dias antes de considerar desatualizado")

def main():
    """
    Função principal do gerenciador de cache
    """
    setup_environment()
    
    if len(sys.argv) < 2:
        mostrar_ajuda()
        return
    
    opcao = sys.argv[1].lower()
    
    if opcao == "status":
        verificar_status_cache()
    elif opcao == "clear":
        limpar_cache_historico()
    elif opcao == "config":
        mostrar_configuracoes_cache()
    elif opcao == "help":
        mostrar_ajuda()
    else:
        print(f"❌ Opção '{opcao}' não reconhecida")
        mostrar_ajuda()

if __name__ == "__main__":
    main()
