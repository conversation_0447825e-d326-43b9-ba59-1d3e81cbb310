#!/usr/bin/env python3
"""
Análise de Correlação entre Ações Brasileiras
- Carrega todas as ações do arquivo acoes-listadas-b3.csv
- Mapa de calor de correlações
- Identificação de 40 ações com baixa correlação para diversificação
- Prioriza ações que pagam dividendos na eliminação por alta correlação
- Exportação para CSV
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import os
warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')
plt.style.use('default')

def carregar_acoes_b3():
    """Carrega todas as ações do arquivo acoes-listadas-b3.csv"""
    try:
        df = pd.read_csv('acoes-listadas-b3.csv')
        # Adicionar .SA aos tickers
        acoes = [f"{ticker}.SA" for ticker in df['Ticker'].tolist()]

        # Criar dicionário de nomes
        nomes = {}
        for _, row in df.iterrows():
            ticker_completo = f"{row['Ticker']}.SA"
            nomes[ticker_completo] = row['Nome']

        print(f"📋 Carregadas {len(acoes)} ações do arquivo acoes-listadas-b3.csv")
        return acoes, nomes

    except FileNotFoundError:
        print("❌ Arquivo acoes-listadas-b3.csv não encontrado!")
        return [], {}
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return [], {}

def verificar_dividendos(ticker):
    """Verifica se a ação paga dividendos nos últimos 2 anos"""
    
    stock = yf.Ticker(ticker)
    dividends = stock.dividends
    

    if dividends.empty:
        return False

    # Verificar se pagou dividendos nos últimos 2 anos
    # Corrigir timezone para comparação correta
    if dividends.index.tz is not None:
        # Se o índice tem timezone, usar timestamp timezone-aware
        dois_anos_atras = pd.Timestamp.now(tz=dividends.index.tz) - pd.Timedelta(days=730)
    else:
        # Se não tem timezone, usar datetime normal
        dois_anos_atras = datetime.now() - timedelta(days=730)

    dividends_recentes = dividends[dividends.index >= dois_anos_atras]

    return len(dividends_recentes) > 0

def verificar_volume_medio(ticker, volume_minimo=100000):
    """Verifica se a ação tem volume médio dos últimos 30 dias >= volume_minimo"""
    try:
        stock = yf.Ticker(ticker)
        # Obter dados dos últimos 45 dias para garantir pelo menos 30 dias úteis
        hist = stock.history(period="45d")

        if hist.empty or len(hist) < 20:  # Mínimo de 20 dias de dados
            return False, 0

        # Pegar os últimos 30 dias de dados disponíveis
        volume_recente = hist['Volume'].tail(30)

        if volume_recente.empty:
            return False, 0

        volume_medio = volume_recente.mean()

        return volume_medio >= volume_minimo, volume_medio

    except Exception as e:
        print(f"   ❌ Erro ao verificar volume de {ticker}: {e}")
        return False, 0

def filtrar_acoes_por_volume(acoes_lista, volume_minimo=100000):
    """Filtra ações por volume médio dos últimos 30 dias"""
    print(f"\n📊 Filtrando ações por volume médio ≥ {volume_minimo:,}...")

    acoes_volume_ok = []
    acoes_volume_baixo = []

    for i, ticker in enumerate(acoes_lista, 1):
        print(f"  [{i:3d}/{len(acoes_lista)}] {ticker.replace('.SA', ''):8s}", end="")

        volume_ok, volume_medio = verificar_volume_medio(ticker, volume_minimo)

        if volume_ok:
            acoes_volume_ok.append(ticker)
            print(f" ✅ Volume: {volume_medio:,.0f}")
        else:
            acoes_volume_baixo.append(ticker)
            print(f" ❌ Volume: {volume_medio:,.0f} (< {volume_minimo:,})")

    print(f"\n📊 Resultado do filtro de volume:")
    print(f"   Ações com volume adequado (≥{volume_minimo:,}): {len(acoes_volume_ok)}")
    print(f"   Ações com volume baixo (<{volume_minimo:,}): {len(acoes_volume_baixo)}")

    return acoes_volume_ok, acoes_volume_baixo

def obter_info_dividendos(acoes_lista):
    """Obtém informações sobre quais ações pagam dividendos"""
    print("\n💰 Verificando quais ações pagam dividendos...")

    acoes_com_dividendos = []
    acoes_sem_dividendos = []

    for i, ticker in enumerate(acoes_lista, 1):
        print(f"  [{i:3d}/{len(acoes_lista)}] {ticker.replace('.SA', ''):8s}", end="")

        paga_dividendos = verificar_dividendos(ticker)

        if paga_dividendos:
            acoes_com_dividendos.append(ticker)
            print(" 💰 Paga dividendos")
        else:
            acoes_sem_dividendos.append(ticker)
            print(" ❌ Não paga dividendos")

    print(f"\n📊 Resultado da análise de dividendos:")
    print(f"   Ações que pagam dividendos: {len(acoes_com_dividendos)}")
    print(f"   Ações que não pagam dividendos: {len(acoes_sem_dividendos)}")

    return acoes_com_dividendos, acoes_sem_dividendos

# Carregar ações e nomes do arquivo CSV
ACOES_ANALISE, NOMES_EMPRESAS = carregar_acoes_b3()

def obter_dados_retornos(acoes, periodo="1y"):
    """Obtém dados de retornos diários das ações - USANDO APENAS ÚLTIMO ANO"""
    print(f"📊 Obtendo dados de {len(acoes)} ações para análise de correlação...")
    print(f"📅 PERÍODO: Apenas último ano (365 dias)")

    # Calcular data de início (1 ano atrás)
    data_fim = datetime.now()
    data_inicio = data_fim - timedelta(days=365)

    dados_precos = pd.DataFrame()
    acoes_validas = []

    for i, ticker in enumerate(acoes, 1):
        try:
            print(f"  [{i:2d}/{len(acoes)}] {ticker.replace('.SA', ''):8s}", end="")

            stock = yf.Ticker(ticker)
            # Usar datas específicas para garantir exatamente 1 ano
            hist = stock.history(start=data_inicio, end=data_fim)

            if len(hist) < 200:  # Mínimo de dados (aproximadamente 8 meses de dados)
                print(" ❌ Dados insuficientes")
                continue

            dados_precos[ticker] = hist['Close']
            acoes_validas.append(ticker)
            print(" ✅")

        except Exception as e:
            print(f" ❌ Erro")
            continue

    if dados_precos.empty:
        return None, []

    # Calcular retornos diários
    retornos = dados_precos.pct_change().dropna()

    print(f"\n✅ Dados obtidos para {len(acoes_validas)} ações")
    print(f"📅 Período: {retornos.index[0].strftime('%Y-%m-%d')} a {retornos.index[-1].strftime('%Y-%m-%d')}")
    print(f"📊 Total de {len(retornos)} dias de dados (último ano)")

    return retornos, acoes_validas

def criar_mapa_calor_correlacao(retornos, acoes_validas):
    """Cria mapa de calor das correlações"""
    print("\n🔥 Criando mapa de calor de correlações...")
    print(retornos)

    # Calcular matriz de correlação
    correlacao = retornos.corr()
    
    # Criar figura
    plt.figure(figsize=(20, 16))
    
    # Criar labels mais limpos
    labels = [ticker.replace('.SA', '') for ticker in acoes_validas]
    
    # Mapa de calor
    mask = np.triu(np.ones_like(correlacao, dtype=bool))  # Mascarar triângulo superior
    
    sns.heatmap(correlacao, 
                mask=mask,
                annot=True, 
                fmt='.2f',
                cmap='RdYlBu_r',
                center=0,
                square=True,
                xticklabels=labels,
                yticklabels=labels,
                cbar_kws={"shrink": .8},
                annot_kws={'size': 7})
    
    plt.title('Mapa de Calor - Correlação entre Ações Brasileiras\n(Retornos Diários - Último Ano)',
              fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('Ações', fontsize=12)
    plt.ylabel('Ações', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('results/figures/correlation_analysis', exist_ok=True)

    plt.savefig('results/figures/correlation_analysis/mapa_calor_correlacao.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 results/figures/correlation_analysis/mapa_calor_correlacao.png")
    
    return correlacao

def criar_mapa_calor_acoes_escolhidas(retornos, acoes_escolhidas):
    """Cria mapa de calor específico das ações escolhidas para diversificação"""
    print("\n🔥 Criando mapa de calor das AÇÕES ESCOLHIDAS...")

    # Filtrar apenas as ações escolhidas
    tickers_escolhidos = [f"{acao['Ticker']}.SA" for acao in acoes_escolhidas]

    # Verificar quais ações escolhidas estão disponíveis nos dados
    tickers_disponiveis = []
    for ticker in tickers_escolhidos:
        if ticker in retornos.columns:
            tickers_disponiveis.append(ticker)

    if len(tickers_disponiveis) < 2:
        print("❌ Dados insuficientes para criar mapa de calor das ações escolhidas")
        return None

    print(f"   📊 {len(tickers_disponiveis)} ações escolhidas com dados disponíveis")

    # Filtrar dados apenas das ações escolhidas
    retornos_escolhidas = retornos[tickers_disponiveis]

    # Calcular matriz de correlação
    correlacao_escolhidas = retornos_escolhidas.corr()

    # Criar figura maior para melhor visualização
    plt.figure(figsize=(16, 14))

    # Criar labels mais limpos
    labels = [ticker.replace('.SA', '') for ticker in tickers_disponiveis]

    # Mapa de calor com anotações maiores
    sns.heatmap(correlacao_escolhidas,
                annot=True,
                fmt='.3f',  # 3 casas decimais para melhor precisão
                cmap='RdYlBu_r',
                center=0,
                square=True,
                xticklabels=labels,
                yticklabels=labels,
                cbar_kws={"shrink": .8},
                annot_kws={'size': 10, 'weight': 'bold'},  # Texto maior e em negrito
                linewidths=0.5)  # Linhas entre células

    plt.title('Mapa de Calor - Correlação entre Ações ESCOLHIDAS para Diversificação\n(Retornos Diários - Último Ano)',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Ações Escolhidas', fontsize=12, fontweight='bold')
    plt.ylabel('Ações Escolhidas', fontsize=12, fontweight='bold')
    plt.xticks(rotation=45, ha='right', fontsize=10)
    plt.yticks(rotation=0, fontsize=10)

    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('results/figures/correlation_analysis', exist_ok=True)

    plt.savefig('results/figures/correlation_analysis/mapa_calor_acoes_escolhidas.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 results/figures/correlation_analysis/mapa_calor_acoes_escolhidas.png")

    # Estatísticas das correlações das ações escolhidas
    correlacoes_valores = []
    for i in range(len(correlacao_escolhidas)):
        for j in range(i+1, len(correlacao_escolhidas)):
            correlacoes_valores.append(correlacao_escolhidas.iloc[i, j])

    if correlacoes_valores:
        print(f"\n📊 Estatísticas das ações escolhidas:")
        print(f"   Correlação média: {np.mean(correlacoes_valores):.3f}")
        print(f"   Correlação mediana: {np.median(correlacoes_valores):.3f}")
        print(f"   Correlação mínima: {min(correlacoes_valores):.3f}")
        print(f"   Correlação máxima: {max(correlacoes_valores):.3f}")

    return correlacao_escolhidas

def analisar_correlacoes(correlacao, acoes_validas, threshold_baixa=0.3):
    """Analisa as correlações e identifica pares com baixa correlação"""
    print(f"\n🔍 Analisando correlações (threshold baixa correlação: ≤{threshold_baixa})...")
    
    # Estatísticas gerais
    correlacoes_valores = []
    for i in range(len(correlacao)):
        for j in range(i+1, len(correlacao)):
            correlacoes_valores.append(correlacao.iloc[i, j])
    
    media_corr = np.mean(correlacoes_valores)
    mediana_corr = np.median(correlacoes_valores)
    std_corr = np.std(correlacoes_valores)
    
    print(f"📊 Estatísticas das correlações:")
    print(f"   Média: {media_corr:.3f}")
    print(f"   Mediana: {mediana_corr:.3f}")
    print(f"   Desvio padrão: {std_corr:.3f}")
    print(f"   Mínima: {min(correlacoes_valores):.3f}")
    print(f"   Máxima: {max(correlacoes_valores):.3f}")
    
    # Encontrar pares com baixa correlação
    pares_baixa_correlacao = []
    
    for i in range(len(correlacao)):
        for j in range(i+1, len(correlacao)):
            corr_valor = correlacao.iloc[i, j]
            if abs(corr_valor) <= threshold_baixa:
                ticker1 = acoes_validas[i]
                ticker2 = acoes_validas[j]
                nome1 = NOMES_EMPRESAS.get(ticker1, ticker1.replace('.SA', ''))
                nome2 = NOMES_EMPRESAS.get(ticker2, ticker2.replace('.SA', ''))
                
                pares_baixa_correlacao.append({
                    'Acao1': ticker1.replace('.SA', ''),
                    'Nome1': nome1,
                    'Acao2': ticker2.replace('.SA', ''),
                    'Nome2': nome2,
                    'Correlacao': corr_valor,
                    'Correlacao_Abs': abs(corr_valor)
                })
    
    # Ordenar por correlação absoluta (menor primeiro)
    pares_baixa_correlacao.sort(key=lambda x: x['Correlacao_Abs'])
    
    print(f"\n🎯 Encontrados {len(pares_baixa_correlacao)} pares com correlação ≤ {threshold_baixa}")
    
    return pares_baixa_correlacao, {
        'media': media_corr,
        'mediana': mediana_corr,
        'std': std_corr,
        'min': min(correlacoes_valores),
        'max': max(correlacoes_valores)
    }

def identificar_acoes_diversificacao(pares_baixa_correlacao, correlacao_matriz, acoes_validas, top_n=32, threshold_alta_correlacao=0.9):
    """Identifica as melhores ações para diversificação, PRIORIZANDO AÇÕES QUE PAGAM DIVIDENDOS desde o início"""
    print(f"\n🎯 Identificando top {top_n} ações para diversificação...")
    print(f"   🔄 NOVA ESTRATÉGIA: Priorizando ações que pagam dividendos PRIMEIRO!")
    print(f"   Eliminando ações com correlação > {threshold_alta_correlacao} entre si...")

    # Obter informações sobre dividendos
    acoes_com_dividendos, acoes_sem_dividendos = obter_info_dividendos([f"{ticker}.SA" for ticker in set([par['Acao1'] for par in pares_baixa_correlacao] + [par['Acao2'] for par in pares_baixa_correlacao])])

    # Contar quantas vezes cada ação aparece em pares de baixa correlação
    contador_acoes = {}

    for par in pares_baixa_correlacao:
        acao1 = par['Acao1']
        acao2 = par['Acao2']

        contador_acoes[acao1] = contador_acoes.get(acao1, 0) + 1
        contador_acoes[acao2] = contador_acoes.get(acao2, 0) + 1

    # Separar ações por dividendos e ordenar por frequência
    acoes_com_div = []
    acoes_sem_div = []

    for acao, freq in contador_acoes.items():
        ticker_completo = f"{acao}.SA"
        if ticker_completo in acoes_com_dividendos:
            acoes_com_div.append((acao, freq))
        else:
            acoes_sem_div.append((acao, freq))

    # Ordenar cada grupo por frequência (maior frequência = menor correlação com outras)
    acoes_com_div = sorted(acoes_com_div, key=lambda x: x[1], reverse=True)
    acoes_sem_div = sorted(acoes_sem_div, key=lambda x: x[1], reverse=True)

    print(f"\n📊 ANÁLISE INICIAL:")
    print(f"   Ações que pagam dividendos: {len(acoes_com_div)}")
    print(f"   Ações que não pagam dividendos: {len(acoes_sem_div)}")

    # FASE 1: Priorizar ações que pagam dividendos
    acoes_selecionadas = []
    acoes_eliminadas = []

    print(f"\n📋 FASE 1 - Seleção PRIORIZANDO DIVIDENDOS:")
    print(f"   🎯 Meta: {top_n} ações (máximo possível com dividendos)")

    # Primeiro: selecionar TODAS as ações que pagam dividendos (limitado por top_n)
    print(f"\n💰 Selecionando ações que PAGAM dividendos:")
    for acao_candidata, freq in acoes_com_div:
        if len(acoes_selecionadas) >= top_n:
            break

        ticker_completo = f"{acao_candidata}.SA"
        nome = NOMES_EMPRESAS.get(ticker_completo, acao_candidata)

        acoes_selecionadas.append({
            'Ticker': acao_candidata,
            'Nome': nome,
            'Frequencia_Baixa_Correlacao': freq,
            'Percentual_Pares': (freq / len(pares_baixa_correlacao)) * 100 if pares_baixa_correlacao else 0
        })
        print(f"   ✅ {acao_candidata:8s} 💰 - Selecionada (freq: {freq})")

    # Segundo: completar com ações que não pagam dividendos (se necessário)
    if len(acoes_selecionadas) < top_n:
        vagas_restantes = top_n - len(acoes_selecionadas)
        print(f"\n❌ Completando com ações que NÃO pagam dividendos ({vagas_restantes} vagas):")

        for acao_candidata, freq in acoes_sem_div:
            if len(acoes_selecionadas) >= top_n:
                break

            ticker_completo = f"{acao_candidata}.SA"
            nome = NOMES_EMPRESAS.get(ticker_completo, acao_candidata)

            acoes_selecionadas.append({
                'Ticker': acao_candidata,
                'Nome': nome,
                'Frequencia_Baixa_Correlacao': freq,
                'Percentual_Pares': (freq / len(pares_baixa_correlacao)) * 100 if pares_baixa_correlacao else 0
            })
            print(f"   ✅ {acao_candidata:8s} ❌ - Selecionada (freq: {freq})")

    # Estatísticas da seleção
    acoes_com_div_selecionadas = [acao for acao in acoes_selecionadas if f"{acao['Ticker']}.SA" in acoes_com_dividendos]
    print(f"\n📊 Resultado da Fase 1:")
    print(f"   Total selecionadas: {len(acoes_selecionadas)}")
    print(f"   Com dividendos: {len(acoes_com_div_selecionadas)} ({len(acoes_com_div_selecionadas)/len(acoes_selecionadas)*100:.1f}%)")
    print(f"   Sem dividendos: {len(acoes_selecionadas) - len(acoes_com_div_selecionadas)} ({(len(acoes_selecionadas) - len(acoes_com_div_selecionadas))/len(acoes_selecionadas)*100:.1f}%)")

    # FASE 2: Eliminação de ações com alta correlação entre si (priorizando dividendos)
    print(f"\n📋 FASE 2 - Eliminando ações com correlação > {threshold_alta_correlacao} (priorizando dividendos):")

    acoes_finais = []
    acoes_removidas = []

    for acao in acoes_selecionadas:
        ticker_atual = f"{acao['Ticker']}.SA"
        paga_dividendos_atual = ticker_atual in acoes_com_dividendos

        # Verificar se a ação atual tem alta correlação com alguma já aprovada
        tem_alta_correlacao = False
        acao_conflitante = None
        correlacao_conflito = 0
        pode_substituir = False

        for i, acao_aprovada in enumerate(acoes_finais):
            ticker_aprovado = f"{acao_aprovada['Ticker']}.SA"
            paga_dividendos_aprovada = ticker_aprovado in acoes_com_dividendos

            try:
                idx_atual = acoes_validas.index(ticker_atual)
                idx_aprovado = acoes_validas.index(ticker_aprovado)

                correlacao_valor = abs(correlacao_matriz.iloc[idx_atual, idx_aprovado])

                if correlacao_valor > threshold_alta_correlacao:
                    tem_alta_correlacao = True
                    acao_conflitante = acao_aprovada['Ticker']
                    correlacao_conflito = correlacao_valor

                    # Se a ação atual paga dividendos e a aprovada não, pode substituir
                    if paga_dividendos_atual and not paga_dividendos_aprovada:
                        pode_substituir = True
                        acao_para_remover = i
                    break

            except ValueError:
                continue

        if tem_alta_correlacao:
            if pode_substituir:
                # Substituir a ação que não paga dividendos
                acao_removida = acoes_finais.pop(acao_para_remover)
                acoes_finais.append(acao)
                print(f"   🔄 {acao['Ticker']:8s} 💰 substituiu {acao_removida['Ticker']} ❌ (prioridade dividendos)")
                acoes_removidas.append({
                    'Ticker': acao_removida['Ticker'],
                    'Nome': acao_removida['Nome'],
                    'Motivo': f'Substituída por ação que paga dividendos ({acao["Ticker"]})',
                    'Frequencia': acao_removida['Frequencia_Baixa_Correlacao']
                })
            else:
                dividendo_status = "💰" if paga_dividendos_atual else "❌"
                print(f"   ❌ {acao['Ticker']:8s} {dividendo_status} - Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}")
                acoes_removidas.append({
                    'Ticker': acao['Ticker'],
                    'Nome': acao['Nome'],
                    'Motivo': f'Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}',
                    'Frequencia': acao['Frequencia_Baixa_Correlacao']
                })
        else:
            dividendo_status = "💰" if paga_dividendos_atual else "❌"
            acoes_finais.append(acao)
            print(f"   ✅ {acao['Ticker']:8s} {dividendo_status} - Aprovada")

    print(f"\n📊 Resultado da Fase 2:")
    print(f"   Ações aprovadas: {len(acoes_finais)}")
    print(f"   Ações removidas por alta correlação: {len(acoes_removidas)}")

    # FASE 3: Backfill para completar 20 ações se necessário
    if len(acoes_finais) < top_n:
        print(f"\n📋 FASE 3 - Backfill para completar {top_n} ações:")
        print(f"   Faltam {top_n - len(acoes_finais)} ações")

        # Recriar lista completa de candidatos (com e sem dividendos)
        acoes_candidatas = acoes_com_div + acoes_sem_div

        # Criar lista de ações restantes (não selecionadas inicialmente)
        acoes_ja_consideradas = {acao['Ticker'] for acao in acoes_selecionadas}
        acoes_restantes = []

        for acao_candidata, freq in acoes_candidatas:
            if acao_candidata not in acoes_ja_consideradas:
                acoes_restantes.append((acao_candidata, freq))

        # Separar ações restantes por dividendos (priorizar as que pagam)
        acoes_restantes_com_dividendos = []
        acoes_restantes_sem_dividendos = []

        for acao_candidata, freq in acoes_restantes:
            ticker_candidato = f"{acao_candidata}.SA"
            if ticker_candidato in acoes_com_dividendos:
                acoes_restantes_com_dividendos.append((acao_candidata, freq))
            else:
                acoes_restantes_sem_dividendos.append((acao_candidata, freq))

        # Tentar primeiro ações que pagam dividendos, depois as outras
        acoes_ordenadas = acoes_restantes_com_dividendos + acoes_restantes_sem_dividendos

        for acao_candidata, freq in acoes_ordenadas:
            if len(acoes_finais) >= top_n:
                break

            ticker_candidato = f"{acao_candidata}.SA"
            paga_dividendos_candidato = ticker_candidato in acoes_com_dividendos

            # Verificar se tem alta correlação com as já aprovadas
            tem_alta_correlacao = False
            acao_conflitante = None
            correlacao_conflito = 0

            for acao_aprovada in acoes_finais:
                ticker_aprovado = f"{acao_aprovada['Ticker']}.SA"

                try:
                    idx_candidato = acoes_validas.index(ticker_candidato)
                    idx_aprovado = acoes_validas.index(ticker_aprovado)

                    correlacao_valor = abs(correlacao_matriz.iloc[idx_candidato, idx_aprovado])

                    if correlacao_valor > threshold_alta_correlacao:
                        tem_alta_correlacao = True
                        acao_conflitante = acao_aprovada['Ticker']
                        correlacao_conflito = correlacao_valor
                        break

                except ValueError:
                    continue

            if not tem_alta_correlacao:
                ticker_completo = f"{acao_candidata}.SA"
                nome = NOMES_EMPRESAS.get(ticker_completo, acao_candidata)
                dividendo_status = "💰" if paga_dividendos_candidato else "❌"

                acoes_finais.append({
                    'Ticker': acao_candidata,
                    'Nome': nome,
                    'Frequencia_Baixa_Correlacao': freq,
                    'Percentual_Pares': (freq / len(pares_baixa_correlacao)) * 100 if pares_baixa_correlacao else 0
                })
                print(f"   ✅ {acao_candidata:8s} {dividendo_status} - Adicionada no backfill (freq: {freq})")
            else:
                dividendo_status = "💰" if paga_dividendos_candidato else "❌"
                print(f"   ❌ {acao_candidata:8s} {dividendo_status} - Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}")
                acoes_eliminadas.append({
                    'Ticker': acao_candidata,
                    'Motivo': f'Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}',
                    'Frequencia': freq
                })

        print(f"\n📊 Resultado da Fase 3:")
        print(f"   Total de ações finais: {len(acoes_finais)}")

    # Consolidar ações eliminadas
    acoes_eliminadas.extend(acoes_removidas)

    print(f"\n📊 RESULTADO FINAL:")
    print(f"   Ações selecionadas: {len(acoes_finais)}")
    print(f"   Ações eliminadas por alta correlação: {len(acoes_eliminadas)}")

    if acoes_eliminadas:
        print(f"\n🚫 Ações eliminadas:")
        for acao in acoes_eliminadas[:10]:  # Mostrar apenas as 10 primeiras
            print(f"   • {acao['Ticker']:8s} - {acao['Motivo']}")
        if len(acoes_eliminadas) > 10:
            print(f"   ... e mais {len(acoes_eliminadas) - 10} ações")

    # Estatísticas sobre dividendos
    acoes_finais_com_dividendos = [acao for acao in acoes_finais if f"{acao['Ticker']}.SA" in acoes_com_dividendos]
    print(f"\n💰 ANÁLISE DE DIVIDENDOS:")
    print(f"   Ações que pagam dividendos: {len(acoes_finais_com_dividendos)}/{len(acoes_finais)} ({len(acoes_finais_com_dividendos)/len(acoes_finais)*100:.1f}%)")
    print(f"   Ações que não pagam dividendos: {len(acoes_finais) - len(acoes_finais_com_dividendos)}/{len(acoes_finais)} ({(len(acoes_finais) - len(acoes_finais_com_dividendos))/len(acoes_finais)*100:.1f}%)")

    return acoes_finais, acoes_eliminadas

def salvar_resultados_csv(pares_baixa_correlacao, melhores_diversificacao, acoes_eliminadas, stats_correlacao):
    """Salva resultados em arquivos CSV"""
    print("\n💾 Salvando resultados em CSV...")

    # Ensure directory exists
    import os
    os.makedirs('results/csv/correlation_data', exist_ok=True)

    # 1. Pares com baixa correlação
    df_pares = pd.DataFrame(pares_baixa_correlacao)
    df_pares.to_csv('results/csv/correlation_data/pares_baixa_correlacao.csv', index=False, encoding='utf-8')
    print("   📄 results/csv/correlation_data/pares_baixa_correlacao.csv")

    # 2. Melhores ações para diversificação
    df_diversificacao = pd.DataFrame(melhores_diversificacao)
    df_diversificacao.to_csv('results/csv/correlation_data/acoes_diversificacao.csv', index=False, encoding='utf-8')
    print("   📄 results/csv/correlation_data/acoes_diversificacao.csv")

    # Salvar também na raiz para compatibilidade
    df_diversificacao.to_csv('acoes_diversificacao.csv', index=False, encoding='utf-8')
    print("   📄 acoes_diversificacao.csv")

    # 3. Ações eliminadas por alta correlação
    if acoes_eliminadas:
        df_eliminadas = pd.DataFrame(acoes_eliminadas)
        df_eliminadas.to_csv('results/csv/correlation_data/acoes_eliminadas_alta_correlacao.csv', index=False, encoding='utf-8')
        print("   📄 results/csv/correlation_data/acoes_eliminadas_alta_correlacao.csv")

    # 4. Estatísticas gerais
    df_stats = pd.DataFrame([stats_correlacao])
    df_stats.to_csv('results/csv/correlation_data/estatisticas_correlacao.csv', index=False, encoding='utf-8')
    print("   📄 results/csv/correlation_data/estatisticas_correlacao.csv")



def criar_graficos_analise(pares_baixa_correlacao, melhores_diversificacao, stats_correlacao):
    """Cria gráficos adicionais de análise"""
    print("\n📊 Criando gráficos de análise...")
    
    _, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # Gráfico 1: Distribuição das correlações
    correlacoes = [par['Correlacao'] for par in pares_baixa_correlacao]
    
    if correlacoes:
        ax1.hist(correlacoes, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(stats_correlacao['media'], color='red', linestyle='--', 
                    label=f'Média Geral: {stats_correlacao["media"]:.3f}')
        ax1.set_title('Distribuição das Correlações\n(Pares com Baixa Correlação ≤0.3)', fontweight='bold')
        ax1.set_xlabel('Correlação')
        ax1.set_ylabel('Frequência')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
    
    # Gráfico 2: Top 15 ações para diversificação
    top_15 = melhores_diversificacao[:15]
    tickers = [item['Ticker'] for item in top_15]
    frequencias = [item['Frequencia_Baixa_Correlacao'] for item in top_15]
    
    bars = ax2.bar(range(len(tickers)), frequencias, color='lightgreen', alpha=0.7)
    ax2.set_title('TOP 15 - Ações para Diversificação', fontweight='bold')
    ax2.set_xlabel('Ações')
    ax2.set_ylabel('Frequência em Pares de Baixa Correlação')
    ax2.set_xticks(range(len(tickers)))
    ax2.set_xticklabels(tickers, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # Adicionar valores nas barras
    for bar, freq in zip(bars, frequencias):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                str(freq), ha='center', va='bottom', fontweight='bold')
    
    # Gráfico 3: Correlações mais baixas (top 20 pares)
    top_20_pares = pares_baixa_correlacao[:20]
    labels_pares = [f"{par['Acao1']}-{par['Acao2']}" for par in top_20_pares]
    correlacoes_top20 = [par['Correlacao'] for par in top_20_pares]
    
    cores = ['green' if c >= 0 else 'red' for c in correlacoes_top20]
    
    ax3.barh(range(len(labels_pares)), correlacoes_top20, color=cores, alpha=0.7)
    ax3.set_title('TOP 20 - Pares com Menor Correlação', fontweight='bold')
    ax3.set_xlabel('Correlação')
    ax3.set_yticks(range(len(labels_pares)))
    ax3.set_yticklabels(labels_pares, fontsize=8)
    ax3.grid(True, alpha=0.3, axis='x')
    ax3.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    
    # Gráfico 4: Estatísticas resumo
    stats_labels = ['Correlação\nMédia', 'Correlação\nMediana', 'Desvio\nPadrão']
    stats_values = [stats_correlacao['media'], stats_correlacao['mediana'], stats_correlacao['std']]
    
    bars4 = ax4.bar(stats_labels, stats_values, color=['blue', 'orange', 'green'], alpha=0.7)
    ax4.set_title('Estatísticas das Correlações', fontweight='bold')
    ax4.set_ylabel('Valor')
    ax4.grid(True, alpha=0.3, axis='y')
    
    # Adicionar valores nas barras
    for bar, val in zip(bars4, stats_values):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{val:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.suptitle('Análise de Correlação - Ações Brasileiras', fontsize=18, fontweight='bold')
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('results/figures/correlation_analysis', exist_ok=True)

    plt.savefig('results/figures/correlation_analysis/analise_correlacao_completa.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 results/figures/correlation_analysis/analise_correlacao_completa.png")

def mostrar_lista_final_acoes(acoes_selecionadas=None):
    """
    Mostra a lista final das ações selecionadas com informações sobre dividendos
    Lê as ações do arquivo acoes_diversificacao.csv se não forem fornecidas
    """
    print("\n" + "=" * 80)
    print("📋 LISTA FINAL DAS AÇÕES SELECIONADAS PARA DIVERSIFICAÇÃO")
    print("=" * 80)

    # Se não foram fornecidas ações, ler do arquivo CSV
    if acoes_selecionadas is None or len(acoes_selecionadas) == 0:
        try:
            import pandas as pd
            df_acoes = pd.read_csv('acoes_diversificacao.csv')
            acoes_selecionadas = []
            for _, row in df_acoes.iterrows():
                acoes_selecionadas.append({
                    'Ticker': row['Ticker'],
                    'Nome': row['Nome'],
                    'Frequencia_Baixa_Correlacao': row['Frequencia_Baixa_Correlacao']
                })
            print(f"📁 Carregadas {len(acoes_selecionadas)} ações do arquivo 'acoes_diversificacao.csv'")
        except Exception as e:
            print(f"❌ Erro ao carregar arquivo 'acoes_diversificacao.csv': {e}")
            return

    if not acoes_selecionadas:
        print("❌ Nenhuma ação foi encontrada!")
        return

    # Verificar dividendos para todas as ações selecionadas
    tickers_completos = [f"{acao['Ticker']}.SA" for acao in acoes_selecionadas]
    acoes_com_dividendos, acoes_sem_dividendos = obter_info_dividendos(tickers_completos)

    print(f"\n📊 RESUMO GERAL:")
    print(f"   Total de ações selecionadas: {len(acoes_selecionadas)}")
    print(f"   Ações que pagam dividendos: {len(acoes_com_dividendos)} ({len(acoes_com_dividendos)/len(acoes_selecionadas)*100:.1f}%)")
    print(f"   Ações que não pagam dividendos: {len(acoes_sem_dividendos)} ({len(acoes_sem_dividendos)/len(acoes_selecionadas)*100:.1f}%)")

    print(f"\n📋 LISTA DETALHADA:")
    print(f"{'#':>3} {'TICKER':>8} {'DIVIDENDO':>10} {'NOME DA EMPRESA':<40} {'FREQ':>6}")
    print("-" * 80)

    for i, acao in enumerate(acoes_selecionadas, 1):
        ticker = acao['Ticker']
        ticker_completo = f"{ticker}.SA"
        nome = acao['Nome'][:40]  # Limitar nome a 40 caracteres
        freq = int(acao['Frequencia_Baixa_Correlacao'])

        # Verificar se paga dividendos
        if ticker_completo in acoes_com_dividendos:
            status_dividendo = "💰 SIM"
        else:
            status_dividendo = "❌ NÃO"

        print(f"{i:>3} {ticker:>8} {status_dividendo:>10} {nome:<40} {freq:>6}")

    # Separar por categoria de dividendos
    print(f"\n💰 AÇÕES QUE PAGAM DIVIDENDOS ({len(acoes_com_dividendos)} ações):")
    acoes_com_div = [acao for acao in acoes_selecionadas if f"{acao['Ticker']}.SA" in acoes_com_dividendos]
    if acoes_com_div:
        tickers_com_div = [acao['Ticker'] for acao in acoes_com_div]
        # Quebrar em linhas de até 10 tickers
        for i in range(0, len(tickers_com_div), 10):
            linha = tickers_com_div[i:i+10]
            print(f"   {', '.join(linha)}")
    else:
        print("   Nenhuma ação que paga dividendos foi selecionada.")

    print(f"\n❌ AÇÕES QUE NÃO PAGAM DIVIDENDOS ({len(acoes_sem_dividendos)} ações):")
    acoes_sem_div = [acao for acao in acoes_selecionadas if f"{acao['Ticker']}.SA" in acoes_sem_dividendos]
    if acoes_sem_div:
        tickers_sem_div = [acao['Ticker'] for acao in acoes_sem_div]
        # Quebrar em linhas de até 10 tickers
        for i in range(0, len(tickers_sem_div), 10):
            linha = tickers_sem_div[i:i+10]
            print(f"   {', '.join(linha)}")
    else:
        print("   Todas as ações selecionadas pagam dividendos!")

    print("\n" + "=" * 80)
    print("💡 Esta lista está salva no arquivo 'acoes_diversificacao.csv'")
    print("🎯 Use essas ações para montar um portfólio diversificado!")
    print("=" * 80)

def main():
    print("🔍 ANÁLISE DE CORRELAÇÃO - AÇÕES BRASILEIRAS")
    print("="*60)
    print(f"📊 Analisando {len(ACOES_ANALISE)} ações brasileiras (B3)")
    print("🎯 Objetivos:")
    print("   • Filtrar ações por volume médio ≥ 100.000 (últimos 30 dias)")
    print("   • Calcular correlações entre todas as ações")
    print("   • Criar mapa de calor visual")
    print("   • Identificar pares com baixa correlação (≤0.3)")
    print("   • Selecionar 40 ações para diversificação")
    print("   • Priorizar ações que pagam dividendos")
    print("   • Exportar resultados para acoes_diversificacao.csv")

    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return

    # 1. Filtrar ações por volume médio
    print("\n" + "="*60)
    print("ETAPA 1: FILTRO POR VOLUME MÉDIO")
    print("="*60)
    acoes_volume_ok, acoes_volume_baixo = filtrar_acoes_por_volume(ACOES_ANALISE, volume_minimo=100000)

    if len(acoes_volume_ok) < 10:
        print("❌ Muito poucas ações passaram no filtro de volume!")
        return

    print(f"\n✅ {len(acoes_volume_ok)} ações passaram no filtro de volume")
    print(f"❌ {len(acoes_volume_baixo)} ações foram descartadas por volume baixo")

    # 2. Obter dados das ações filtradas
    print("\n" + "="*60)
    print("ETAPA 2: OBTENÇÃO DE DADOS DE PREÇOS")
    print("="*60)
    retornos, acoes_validas = obter_dados_retornos(acoes_volume_ok)

    if retornos is None or len(acoes_validas) < 10:
        print("❌ Dados insuficientes para análise!")
        return

    # 3. Criar mapa de calor
    print("\n" + "="*60)
    print("ETAPA 3: ANÁLISE DE CORRELAÇÃO")
    print("="*60)
    correlacao = criar_mapa_calor_correlacao(retornos, acoes_validas)

    # 4. Analisar correlações
    pares_baixa_correlacao, stats_correlacao = analisar_correlacoes(correlacao, acoes_validas)

    # 5. Identificar ações para diversificação (com eliminação de alta correlação)
    print("\n" + "="*60)
    print("ETAPA 4: SELEÇÃO DE AÇÕES PARA DIVERSIFICAÇÃO")
    print("="*60)
    melhores_diversificacao, acoes_eliminadas = identificar_acoes_diversificacao(
        pares_baixa_correlacao, correlacao, acoes_validas)

    # 6. Criar mapa de calor específico das ações escolhidas
    print("\n" + "="*60)
    print("ETAPA 5: VISUALIZAÇÕES E RELATÓRIOS")
    print("="*60)
    correlacao_escolhidas = criar_mapa_calor_acoes_escolhidas(retornos, melhores_diversificacao)

    # 7. Salvar resultados
    salvar_resultados_csv(pares_baixa_correlacao, melhores_diversificacao, acoes_eliminadas, stats_correlacao)

    # 8. Criar gráficos de análise
    criar_graficos_analise(pares_baixa_correlacao, melhores_diversificacao, stats_correlacao)
    
    # 9. Relatório final
    print("\n" + "="*80)
    print("📈 RELATÓRIO FINAL - ANÁLISE DE CORRELAÇÃO COM FILTRO DE VOLUME")
    print("="*80)

    print(f"\n📊 FILTRO DE VOLUME:")
    print(f"   Ações iniciais: {len(ACOES_ANALISE)}")
    print(f"   Ações com volume ≥ 100.000: {len(acoes_volume_ok)}")
    print(f"   Ações descartadas por volume baixo: {len(acoes_volume_baixo)}")
    print(f"   Taxa de aprovação no filtro: {len(acoes_volume_ok)/len(ACOES_ANALISE)*100:.1f}%")

    print(f"\n🔢 ESTATÍSTICAS DE CORRELAÇÃO:")
    print(f"   Ações analisadas (após filtro): {len(acoes_validas)}")
    print(f"   Pares com baixa correlação (≤0.3): {len(pares_baixa_correlacao)}")
    print(f"   Correlação média: {stats_correlacao['media']:.3f}")
    print(f"   Correlação mediana: {stats_correlacao['mediana']:.3f}")

    print(f"\n🎯 PROCESSO DE SELEÇÃO:")
    print(f"   Ações selecionadas para diversificação: {len(melhores_diversificacao)}")
    print(f"   Ações eliminadas por alta correlação (>0.9): {len(acoes_eliminadas)}")

    print(f"\n🎯 TOP 10 AÇÕES SELECIONADAS PARA DIVERSIFICAÇÃO:")
    for i, acao in enumerate(melhores_diversificacao[:10], 1):
        print(f"{i:2d}. {acao['Ticker']:8s} - {acao['Nome']:20s} "
              f"({acao['Frequencia_Baixa_Correlacao']} pares)")

    if acoes_eliminadas:
        print(f"\n� TOP 5 AÇÕES ELIMINADAS POR ALTA CORRELAÇÃO:")
        for i, acao in enumerate(acoes_eliminadas[:5], 1):
            print(f"{i:2d}. {acao['Ticker']:8s} - {acao['Motivo']}")

    print(f"\n�🔗 TOP 10 PARES COM MENOR CORRELAÇÃO:")
    for i, par in enumerate(pares_baixa_correlacao[:10], 1):
        print(f"{i:2d}. {par['Acao1']:8s} x {par['Acao2']:8s} "
              f"= {par['Correlacao']:+.3f}")

    print(f"\n📁 ARQUIVOS GERADOS:")
    print("   • results/figures/correlation_analysis/mapa_calor_correlacao.png (todas as ações)")
    print("   • results/figures/correlation_analysis/mapa_calor_acoes_escolhidas.png (ações escolhidas)")
    print("   • results/figures/correlation_analysis/analise_correlacao_completa.png")
    print("   • results/csv/correlation_data/pares_baixa_correlacao.csv")
    print("   • results/csv/correlation_data/acoes_diversificacao.csv")
    if acoes_eliminadas:
        print("   • results/csv/correlation_data/acoes_eliminadas_alta_correlacao.csv")
    print("   • results/csv/correlation_data/estatisticas_correlacao.csv")

    print(f"\n✅ Análise de correlação com filtro de volume concluída!")
    print(f"\n💡 DICA: Use as ações do arquivo 'acoes_diversificacao.csv'")
    print("   para montar um portfólio diversificado!")
    print("📊 Filtro aplicado: Volume médio ≥ 100.000 (últimos 30 dias)")
    print("💰 Prioridade foi dada para ações que pagam dividendos")

    # Mostrar lista final das ações selecionadas com status de dividendos
    # Ler do arquivo CSV para mostrar as ações que realmente estão salvas
    mostrar_lista_final_acoes()

if __name__ == "__main__":
    main()
