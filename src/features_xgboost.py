#!/usr/bin/env python3
"""
Módulo de features compartilhadas para XGBoost
Contém todas as funções de cálculo de features utilizadas tanto pelo classificador
quanto pelo estimador XGBoost para garantir consistência.
"""

import pandas as pd
import numpy as np
import holidays
from datetime import datetime, timedelta
import sys

import os
from scipy.signal import butter, lfilter, lfiltic

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling
from config_loader import config


def corrigir_valores_zero_ultimo_dia(dados):
    """
    Corrige valores zero no último dia substituindo pelo penúltimo dia
    """
    if len(dados) < 2:
        print(f"     ⚠️ Dados insuficientes para correção: {len(dados)} dias")
        return dados

    # Remover duplicatas no índice se existirem
    if dados.index.duplicated().any():
        print(f"     🔧 Removendo {dados.index.duplicated().sum()} índices duplicados")
        dados = dados[~dados.index.duplicated(keep='last')]

    # Verificar novamente se temos dados suficientes
    if len(dados) < 2:
        print(f"     ⚠️ Dados insuficientes após limpeza: {len(dados)} dias")
        return dados

    # Colunas OHLC para verificar
    colunas_ohlc = ['Open', 'High', 'Low', 'Close']

    # Verificar e corrigir valores zero no último dia
    for coluna in colunas_ohlc:
        if coluna in dados.columns:
            try:
                # Obter o último valor como escalar (forçar conversão)
                ultimo_valor = dados[coluna].iloc[-1]

                # Se for uma Series, pegar o primeiro valor
                if isinstance(ultimo_valor, pd.Series):
                    ultimo_valor = ultimo_valor.iloc[0] if len(ultimo_valor) > 0 else None

                # Verificar se é zero ou NaN
                if pd.isna(ultimo_valor) or ultimo_valor == 0:
                    # Obter penúltimo valor
                    penultimo_valor = dados['Close'].iloc[-2]
                    if isinstance(penultimo_valor, pd.Series):
                        penultimo_valor = penultimo_valor.iloc[0] if len(penultimo_valor) > 0 else ultimo_valor

                    dados.loc[dados.index[-1], coluna] = penultimo_valor
                    print(f"     🔧 Corrigido valor zero em {coluna}: {ultimo_valor} → {penultimo_valor}")

            except Exception as e:
                print(f"     ⚠️ Erro ao corrigir {coluna}: {e}")
                continue

    return dados


def calcular_rolling_hibrido(serie_atual, serie_historica, window, operacao='mean'):
    """
    Calcula rolling usando valores históricos (com Close) para o passado
    e valor atual (sem Close) apenas para o último ponto

    Args:
        serie_atual: Série com valores atuais (sem Close do dia atual)
        serie_historica: Série com valores históricos (com Close completo)
        window: Janela para o rolling
        operacao: 'mean', 'sum', 'std', etc.
    """
    resultado = pd.Series(index=serie_atual.index, dtype=float)

    for i in range(len(serie_atual)):
        if i < window - 1:
            # Não há dados suficientes para a janela completa
            resultado.iloc[i] = np.nan
        else:
            # Para cada posição i, usar:
            # - Valores históricos (com Close) para posições i-window+1 até i-1
            # - Valor atual (sem Close) apenas para posição i
            janela_valores = []

            # Adicionar valores históricos (posições anteriores)
            for j in range(i - window + 1, i):
                janela_valores.append(serie_historica.iloc[j])

            # Adicionar valor atual (posição i)
            janela_valores.append(serie_atual.iloc[i])

            # Aplicar operação
            janela_array = np.array(janela_valores)
            if operacao == 'mean':
                resultado.iloc[i] = np.mean(janela_array)
            elif operacao == 'sum':
                resultado.iloc[i] = np.sum(janela_array)
            elif operacao == 'std':
                resultado.iloc[i] = np.std(janela_array)
            elif operacao == 'median':
                resultado.iloc[i] = np.median(janela_array)

    return resultado


def calcular_features_econometricas_ohlcv(dados):
    """
    Calcula features econométricas avançadas usando dados OHLCV

    SIMULAÇÃO REALISTA: Para cada dia durante a estimativa ao longo do ano,
    não usa o valor de Close do próprio dia (evita data leakage)

    ROLLING HÍBRIDO: Para operações rolling, usa Close histórico para valores
    passados e apenas o valor atual sem Close para o último ponto
    """

    # SIMULAÇÃO REALÍSTICA: Criar price_reference sem usar Close do próprio dia
    # Para cada dia i, usar apenas informações disponíveis até o dia i-1
    price_reference = dados['Close'].copy()

    # Para simulação realística: substituir Close de cada dia por OHL
    # (simula que Close não está disponível durante o trading do dia)
    for i in range(1, len(dados)):  # Começar do dia 1 (dia 0 pode usar Close histórico)
        price_reference.iloc[i] = (
            dados['Open'].iloc[i] + dados['High'].iloc[i] + dados['Low'].iloc[i]
        ) / 3

    # TAMBÉM calcular features históricas usando Close completo para versões lagged
    price_reference_historical = dados['Close'].copy()  # Usar Close histórico completo

    # 1. Volatilidade de Parkinson (usa High-Low range) - não depende de Close
    dados['Parkinson_Volatility'] = np.sqrt((1 / (4 * np.log(2))) *
                                            ((np.log(dados['High'] / dados['Low'])**2)))

    # 2. Índice de Fluxo de Dinheiro (MFI - Money Flow Index)
    # Versão atual (sem Close do dia atual)
    typical_price = (dados['High'] + dados['Low'] + price_reference) / 3
    money_flow = typical_price * dados['Volume']
    delta_tp = typical_price.diff()

    # Versão histórica (com Close histórico) para rolling híbrido
    typical_price_hist = (dados['High'] + dados['Low'] + price_reference_historical) / 3
    money_flow_hist = typical_price_hist * dados['Volume']
    delta_tp_hist = typical_price_hist.diff()

    # Usar rolling híbrido para MFI
    positive_flow_atual = money_flow.where(delta_tp > 0, 0)
    positive_flow_hist = money_flow_hist.where(delta_tp_hist > 0, 0)
    positive_flow = calcular_rolling_hibrido(positive_flow_atual, positive_flow_hist, 14, 'sum')

    negative_flow_atual = money_flow.where(delta_tp < 0, 0)
    negative_flow_hist = money_flow_hist.where(delta_tp_hist < 0, 0)
    negative_flow = calcular_rolling_hibrido(negative_flow_atual, negative_flow_hist, 14, 'sum')

    # Evitar divisão por zero no MFI
    mf_ratio = positive_flow / negative_flow.replace(0, np.nan)
    dados['MFI'] = 100 - (100 / (1 + mf_ratio))

    # Versão histórica completa para features lagged
    positive_flow_hist_full = money_flow_hist.where(delta_tp_hist > 0, 0).rolling(window=14).sum()
    negative_flow_hist_full = money_flow_hist.where(delta_tp_hist < 0, 0).rolling(window=14).sum()
    mf_ratio_hist = positive_flow_hist_full / negative_flow_hist_full.replace(0, np.nan)
    dados['MFI_Historical'] = 100 - (100 / (1 + mf_ratio_hist))

    # 3. Índice de Facilidade de Movimento (EMV - Ease of Movement) - não depende de Close
    distance_moved = ((dados['High'] + dados['Low']) / 2) - ((dados['High'].shift(1) + dados['Low'].shift(1)) / 2)
    emv_divisor = config.get('xgboost.calculations.emv_volume_divisor', 1000000)

    # Evitar divisão por zero quando High == Low
    high_low_diff = dados['High'] - dados['Low']
    high_low_diff = high_low_diff.replace(0, np.nan)  # Substituir zeros por NaN

    # Evitar divisão por zero no EMV quando volume é zero
    volume_safe_emv = dados['Volume'].replace(0, np.nan)
    box_ratio = (volume_safe_emv / emv_divisor) / high_low_diff
    dados['EMV'] = distance_moved / box_ratio

    # EMV_MA não depende de Close, mas ainda pode se beneficiar do rolling híbrido
    # (mesmo valor atual e histórico, mas mantém consistência)
    dados['EMV_MA'] = calcular_rolling_hibrido(dados['EMV'], dados['EMV'], 14, 'mean')

    # 4. Índice de Amihud (Illiquidity)
    # Tratar volumes zero para evitar divisão por zero
    volume_safe = dados['Volume'].replace(0, np.nan)

    # Versão atual (sem Close do dia atual)
    dados['Amihud'] = abs(price_reference.pct_change()) / volume_safe
    # Versão histórica (com Close histórico) para features lagged
    dados['Amihud_Historical'] = abs(price_reference_historical.pct_change()) / volume_safe

    # 5. Roll Spread (estimativa de bid-ask spread)
    def calcular_roll_spread_hibrido(price_atual, price_hist, window):
        """Calcula Roll Spread usando rolling híbrido"""
        resultado = pd.Series(index=price_atual.index, dtype=float)

        for i in range(len(price_atual)):
            if i < window - 1:
                resultado.iloc[i] = np.nan
            else:
                # Construir janela híbrida
                janela_valores = []
                for j in range(i - window + 1, i):
                    janela_valores.append(price_hist.iloc[j])
                janela_valores.append(price_atual.iloc[i])

                # Calcular diferenças
                diffs = np.diff(janela_valores)
                if len(diffs) > 1:
                    try:
                        cov_val = np.cov(diffs[:-1], diffs[1:])[0, 1]
                        resultado.iloc[i] = 2 * np.sqrt(np.abs(cov_val))
                    except:
                        resultado.iloc[i] = 0
                else:
                    resultado.iloc[i] = 0

        return resultado

    # Versão atual (sem Close do dia atual)
    dados['Roll_Spread'] = calcular_roll_spread_hibrido(price_reference, price_reference_historical, 20)

    # Versão histórica (com Close histórico) para features lagged
    price_change_hist = price_reference_historical.diff()
    cov_hist = price_change_hist.rolling(window=20).apply(
        lambda x: np.cov(x[:-1], x[1:])[0, 1] if len(x) > 1 else 0, raw=False)
    dados['Roll_Spread_Historical'] = 2 * np.sqrt(np.abs(cov_hist))

    # 6. Índice de Hurst
    def hurst_simple(ts):
        try:
            if len(ts) < 20:
                return np.nan
            # Usar método R/S simplificado
            ts = np.array(ts)
            ts = ts[~np.isnan(ts)]  # Remover NaN
            if len(ts) < 20:
                return np.nan

            # Calcular retornos
            returns = np.diff(np.log(ts))
            if len(returns) < 10:
                return np.nan

            # Calcular R/S para diferentes lags
            lags = [5, 10, 15]
            rs_values = []

            for lag in lags:
                if len(returns) < lag:
                    continue
                # Dividir em blocos
                n_blocks = len(returns) // lag
                if n_blocks < 2:
                    continue

                rs_block = []
                for i in range(n_blocks):
                    block = returns[i*lag:(i+1)*lag]
                    if len(block) < lag:
                        continue

                    # Calcular R/S para este bloco
                    mean_block = np.mean(block)
                    deviations = np.cumsum(block - mean_block)
                    R = np.max(deviations) - np.min(deviations)
                    S = np.std(block)

                    if S > 0:
                        rs_block.append(R / S)

                if rs_block:
                    rs_values.append(np.mean(rs_block))

            if len(rs_values) >= 2:
                # Estimar H usando regressão simples
                log_lags = np.log(lags[:len(rs_values)])
                log_rs = np.log(rs_values)
                if len(log_lags) >= 2:
                    H = np.polyfit(log_lags, log_rs, 1)[0]
                    return max(0, min(1, H))  # Limitar entre 0 e 1

            return 0.5  # Valor neutro se não conseguir calcular
        except:
            return 0.5

    # Versão atual (sem Close do dia atual)
    dados['Hurst'] = price_reference.rolling(window=50).apply(
        lambda x: hurst_simple(x), raw=True)
    # Versão histórica (com Close histórico) para features lagged
    dados['Hurst_Historical'] = price_reference_historical.rolling(window=50).apply(
        lambda x: hurst_simple(x), raw=True)

    # 8. Volatilidade Histórica Normalizada pelo Volume
    # Versão atual (sem Close do dia atual)
    returns_atual = price_reference.pct_change()
    returns_hist = price_reference_historical.pct_change()
    vol_returns = calcular_rolling_hibrido(returns_atual, returns_hist, 20, 'std')

    vol_volume = calcular_rolling_hibrido(dados['Volume'], dados['Volume'], 20, 'mean')
    vol_volume = np.sqrt(vol_volume)
    vol_volume = vol_volume.replace(0, np.nan)
    dados['Vol_per_Volume'] = vol_returns / vol_volume

    # Versão histórica (com Close histórico) para features lagged
    vol_returns_hist_full = price_reference_historical.pct_change().rolling(window=20).std()
    vol_volume_hist = np.sqrt(dados['Volume'].rolling(window=20).mean())
    vol_volume_hist = vol_volume_hist.replace(0, np.nan)
    dados['Vol_per_Volume_Historical'] = vol_returns_hist_full / vol_volume_hist

    # 9. Índice de Chaikin Money Flow (CMF)
    high_low_diff = dados['High'] - dados['Low']
    high_low_diff = high_low_diff.replace(0, np.nan)

    # Versão atual (sem Close do dia atual)
    money_flow_multiplier = ((price_reference - dados['Low']) - (dados['High'] - price_reference))/high_low_diff
    money_flow_volume = money_flow_multiplier * dados['Volume']

    # Versão histórica (com Close histórico)
    money_flow_multiplier_hist = ((price_reference_historical - dados['Low']) - (dados['High'] - price_reference_historical))/high_low_diff
    money_flow_volume_hist = money_flow_multiplier_hist * dados['Volume']

    # Usar rolling híbrido para CMF
    money_flow_sum = calcular_rolling_hibrido(money_flow_volume, money_flow_volume_hist, 20, 'sum')
    volume_sum = calcular_rolling_hibrido(dados['Volume'], dados['Volume'], 20, 'sum')
    volume_sum = volume_sum.replace(0, np.nan)
    dados['CMF'] = money_flow_sum / volume_sum

    # Versão histórica completa para features lagged
    volume_sum_hist = dados['Volume'].rolling(window=20).sum()
    volume_sum_hist = volume_sum_hist.replace(0, np.nan)
    dados['CMF_Historical'] = money_flow_volume_hist.rolling(window=20).sum() / volume_sum_hist

    # 10. Índice de Acumulação/Distribuição (A/D)
    high_low_diff = dados['High'] - dados['Low']
    high_low_diff = high_low_diff.replace(0, np.nan)

    # Versão atual (sem Close do dia atual)
    ad = ((price_reference - dados['Low']) - (dados['High'] - price_reference))/high_low_diff*dados['Volume']
    dados['AD_Line'] = ad.cumsum()

    # Versão histórica (com Close histórico) para features lagged
    ad_hist = ((price_reference_historical - dados['Low']) - (dados['High'] - price_reference_historical))/high_low_diff*dados['Volume']
    dados['AD_Line_Historical'] = ad_hist.cumsum()

    # 11. Oscilador de Volume (VO) - não depende de Close
    vol_ma_5 = calcular_rolling_hibrido(dados['Volume'], dados['Volume'], 5, 'mean')
    vol_ma_10 = calcular_rolling_hibrido(dados['Volume'], dados['Volume'], 10, 'mean')
    vol_ma_10 = vol_ma_10.replace(0, np.nan)
    dados['VO'] = (vol_ma_5 / vol_ma_10 - 1) * 100

    # 12. Valor máximo dos últimos 50 dias (High_Max_50)
    dados['High_Max_50'] = dados['High'].rolling(window=50).max()

    # 13. Valor mínimo dos últimos 50 dias (Low_Min_50)
    dados['Low_Min_50'] = dados['Low'].rolling(window=50).min()

    # Preencher valores infinitos e NaN com valores apropriados
    econometric_features = ['Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
                            'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
                            'High_Max_50', 'Low_Min_50']

    # Incluir também as versões históricas
    econometric_features_historical = ['MFI_Historical', 'Amihud_Historical', 'Roll_Spread_Historical',
                                      'Hurst_Historical', 'Vol_per_Volume_Historical', 'CMF_Historical', 'AD_Line_Historical']

    all_features = econometric_features + econometric_features_historical

    for feature in all_features:
        if feature in dados.columns:
            # Substituir infinitos por NaN
            dados[feature] = dados[feature].replace([np.inf, -np.inf], np.nan)
            # Preencher NaN com mediana da série (determinístico)
            mediana = dados[feature].median()
            if pd.isna(mediana):
                mediana = 0.0  # Fallback determinístico
            dados[feature] = dados[feature].fillna(mediana)

    return dados


def calcular_features_e_sinais(dados, ticker=None, tickers_carteira=None):
    """
    Calcula features e sinais de compra/venda baseados no pct_change da média OHLC.

    LÓGICA DE PREVISÃO:
    - Para prever o dia D (ex: 21/07/25), usa:
      * Dados históricos até D-1 (ex: 18/07/25 - último dia útil)
      * Features temporais do dia D (ex: segunda-feira, julho, etc.)
    - O modelo prevê "o que vai acontecer HOJE" usando dados até "ontem"

    Evitar data_leakage: Não usa valores OHLCV do próprio dia para previsão
    """

    # Corrigir valores zero no último dia
    dados = corrigir_valores_zero_ultimo_dia(dados)

    # ================= INCORPORAÇÃO DOS SINAIS BUTTERWORTH =================
    # Calcular médias móveis Butterworth MM10, MM25, MM100 se não existirem
    # (evita sobrescrever caso já estejam presentes)
    if not all(col in dados.columns for col in ['MM10', 'MM25', 'MM100']):
        # Parâmetros do filtro Butterworth
        try:
            butterworth_config = config.get_butterworth_config()
            filter_order = butterworth_config['filter_order']
            cutoff_frequencies = butterworth_config['cutoff_frequencies']
            filter_type = butterworth_config['filter_type']
            analog = butterworth_config['analog']
        except Exception:
            # Fallback: valores padrão
            filter_order = 2
            cutoff_frequencies = {'mm10': 0.09, 'mm25': 0.035, 'mm100': 0.009}
            filter_type = 'low'
            analog = False

        # Calcular média OHLC se não existir
        if 'Media_OHLC' not in dados.columns:
            dados['Media_OHLC'] = (dados['Open'] + dados['High'] + dados['Low'] + dados['Close']) / 4

        # MM10
        if 'MM10' not in dados.columns:
            b, a = butter(filter_order, cutoff_frequencies['mm10'], btype=filter_type, analog=analog)
            zi = lfiltic(b, a, dados['Media_OHLC'].iloc[:len(b)].values, dados['Media_OHLC'].iloc[:len(a)].values)
            filtered_signal, _ = lfilter(b, a, dados['Media_OHLC'], zi=zi)
            dados['MM10'] = filtered_signal

        # MM25
        if 'MM25' not in dados.columns:
            b, a = butter(filter_order, cutoff_frequencies['mm25'], btype=filter_type, analog=analog)
            zi = lfiltic(b, a, dados['Media_OHLC'].iloc[:len(b)].values, dados['Media_OHLC'].iloc[:len(a)].values)
            filtered_signal, _ = lfilter(b, a, dados['Media_OHLC'], zi=zi)
            dados['MM25'] = filtered_signal

        # MM100
        if 'MM100' not in dados.columns:
            b, a = butter(filter_order, cutoff_frequencies['mm100'], btype=filter_type, analog=analog)
            zi = lfiltic(b, a, dados['Media_OHLC'].iloc[:len(b)].values, dados['Media_OHLC'].iloc[:len(a)].values)
            filtered_signal, _ = lfilter(b, a, dados['Media_OHLC'], zi=zi)
            dados['MM100'] = filtered_signal

    # ======================================================================

    # Calcular média OHLC sem usar Close do próprio dia
    # Para cada dia i, usar apenas informações disponíveis até o dia i-1

    # Criar arrays para cálculo vetorizado (mais eficiente que loop)
    media_ohlc = np.zeros(len(dados))

    # Primeiro dia: usar OHLC completo (não há predição para fazer)
    media_ohlc[0] = (
        dados['Open'].iloc[0] + dados['High'].iloc[0] +
        dados['Low'].iloc[0] + dados['Close'].iloc[0]
    ) / 4

    # Dias subsequentes: simular trading realístico
    # Para predição do dia i, usar apenas OHL do dia i (Close não disponível durante o dia)
    if len(dados) > 1:
        media_ohlc[1:] = (
            dados['Open'].iloc[1:].values +
            dados['High'].iloc[1:].values +
            dados['Low'].iloc[1:].values
        ) / 3

    # Atribuir ao DataFrame de uma vez (mais eficiente)
    dados['Media_OHLC'] = media_ohlc

    # Calcular pct_change da média OHLC (variação percentual em relação ao dia anterior)
    dados['Media_OHLC_PctChange'] = dados['Media_OHLC'].pct_change()

    # NOVA LÓGICA: Calcular pct_change individual para cada componente OHLC
    # Para cada dia i, usar apenas informações disponíveis até o dia i-1

    # Open: sempre disponível no início do dia
    dados['Open_PctChange'] = dados['Open'].pct_change()

    # High e Low: disponíveis durante o dia
    dados['High_PctChange'] = dados['High'].pct_change()
    dados['Low_PctChange'] = dados['Low'].pct_change()

    # Close: para trading realístico, usar versão histórica para lags
    dados['Close_PctChange'] = dados['Close'].pct_change()
    dados['Close_PctChange_Historical'] = dados['Close_PctChange'].copy()  # Para lags

    # Usar configurações do XGBoost
    volatility_window = config.get('xgboost.features.volatility_window')
    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')
    pct_threshold = config.get('xgboost.features.pct_threshold')

    # Calcular volatilidade usando janela configurada
    dados['Volatilidade'] = dados['Media_OHLC_PctChange'].rolling(window=volatility_window).std()*100

    # Calcular spread bid-ask usando a função edge_rolling
    spread_series = edge_rolling(dados, window=volatility_window)
    dados['Spread'] = spread_series*100  # Converter para percentual

    # Preencher NaN
    dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())
    dados['Volatilidade'] = dados['Volatilidade'].fillna(dados['Volatilidade'].mean())

    # Calcular features econométricas baseadas em OHLCV
    dados = calcular_features_econometricas_ohlcv(dados)

    # Adicionar features one-hot encoding para dias da semana (segunda a sexta)
    # Monday=0, Tuesday=1, Wednesday=2, Thursday=3, Friday=4, Saturday=5, Sunday=6
    weekday = dados.index.dayofweek
    dados['Segunda'] = (weekday == 0).astype(int)  # Monday
    dados['Terca'] = (weekday == 1).astype(int)    # Tuesday
    dados['Quarta'] = (weekday == 2).astype(int)   # Wednesday
    dados['Quinta'] = (weekday == 3).astype(int)   # Thursday
    dados['Sexta'] = (weekday == 4).astype(int)    # Friday

    # Adicionar features one-hot encoding para meses (1 a 12)
    month = dados.index.month
    for i in range(1, 13):
        dados[f'Mes_{i}'] = (month == i).astype(int)

    # Adicionar features one-hot encoding para quarters do ano (Q1 a Q4)
    # Q1: Janeiro-Março (meses 1,2,3), Q2: Abril-Junho (meses 4,5,6)
    # Q3: Julho-Setembro (meses 7,8,9), Q4: Outubro-Dezembro (meses 10,11,12)
    dados['Quarter_1'] = ((month >= 1) & (month <= 3)).astype(int)  # Q1: Jan-Mar
    dados['Quarter_2'] = ((month >= 4) & (month <= 6)).astype(int)  # Q2: Abr-Jun
    dados['Quarter_3'] = ((month >= 7) & (month <= 9)).astype(int)  # Q3: Jul-Set
    dados['Quarter_4'] = ((month >= 10) & (month <= 12)).astype(int)  # Q4: Out-Dez

    # Adicionar feature para último dia de cada quarter
    # Identifica se é o último dia útil disponível nos dados para cada quarter
    def is_last_day_of_quarter(date_index):
        """
        Determina se cada data é o último dia útil de um quarter nos dados disponíveis
        """
        last_quarter_days = pd.Series(0, index=date_index, dtype=int)

        # Agrupar por ano e quarter
        df_temp = pd.DataFrame({'date': date_index})
        df_temp['year'] = df_temp['date'].dt.year
        df_temp['quarter'] = df_temp['date'].dt.quarter

        # Para cada combinação ano-quarter, encontrar o último dia disponível
        for (year, quarter), group in df_temp.groupby(['year', 'quarter']):
            last_date_idx = group['date'].idxmax()  # Índice da última data do grupo
            last_quarter_days.iloc[last_date_idx] = 1

        return last_quarter_days

    dados['Last_Day_Quarter'] = is_last_day_of_quarter(dados.index)

    # Adicionar feature para dias que antecedem feriados brasileiros
    def calcular_pre_feriados_brasil(date_index):
        """
        Calcula feature binária indicando dias que antecedem feriados brasileiros

        Args:
            date_index: Índice de datas do DataFrame

        Returns:
            Series com 1 para dias que antecedem feriados e 0 caso contrário
        """
        # Criar instância dos feriados brasileiros
        br_holidays = holidays.Brazil()

        # Inicializar série com zeros
        pre_holiday_feature = pd.Series(0, index=date_index, dtype=int)

        for i, current_date in enumerate(date_index):
            # Verificar o próximo dia útil
            next_date = current_date + pd.Timedelta(days=1)

            # Se o próximo dia for feriado, marcar o dia atual como pré-feriado
            if next_date.strftime('%Y-%m-%d') in br_holidays:
                pre_holiday_feature.iloc[i] = 1
            else:
                # Verificar também se o próximo dia útil (pulando fins de semana) é feriado
                # Isso é útil para sextas-feiras que antecedem feriados de segunda
                days_ahead = 1
                while days_ahead <= 3:  # Verificar até 3 dias à frente (para cobrir fins de semana)
                    check_date = current_date + pd.Timedelta(days=days_ahead)

                    # Se encontrou um dia útil (segunda a sexta)
                    if check_date.weekday() < 5:  # 0=segunda, 4=sexta
                        if check_date.strftime('%Y-%m-%d') in br_holidays:
                            pre_holiday_feature.iloc[i] = 1
                        break  # Parar na primeira data útil encontrada

                    days_ahead += 1

        return pre_holiday_feature

    dados['Pre_Feriado_Brasil'] = calcular_pre_feriados_brasil(dados.index)

    # NOVA LÓGICA: Criar sinais baseados na comparação com o dia anterior
    # Para treinar: comparar dia atual vs dia anterior (mais realístico)
    # Para prever: usar modelo treinado para prever se hoje será melhor que ontem

    # Calcular média OHLC do dia anterior (shift +1 para pegar valor anterior)
    dados['Media_OHLC_Anterior'] = dados['Media_OHLC'].shift(1)

    # SINAIS DE TREINAMENTO: baseados na comparação atual vs anterior
    # Sinal de compra: média OHLC atual > média OHLC do dia anterior (acima do threshold)
    # Exemplo: dia 18/07 tem sinal de compra se OHLC[18] > OHLC[17] + threshold
    dados['Sinal_Compra'] = ((dados['Media_OHLC'] - dados['Media_OHLC_Anterior']) / dados['Media_OHLC_Anterior'] * 100 > pct_threshold).astype(int)

    # Sinal de venda: média OHLC atual < média OHLC do dia anterior (abaixo do threshold)
    # Exemplo: dia 18/07 tem sinal de venda se OHLC[18] < OHLC[17] - threshold
    dados['Sinal_Venda'] = ((dados['Media_OHLC'] - dados['Media_OHLC_Anterior']) / dados['Media_OHLC_Anterior'] * 100 < -pct_threshold).astype(int)

    # Para compatibilidade com código existente, manter Media_OHLC_Futura como cópia da atual
    # (não é mais usada para sinais, mas pode ser usada em relatórios)
    dados['Media_OHLC_Futura'] = dados['Media_OHLC']




    # NOVA LÓGICA: Criar features individuais de OHLC com lags
    for i in range(1, ohlc_lags + 1):
        # Open: sempre disponível, usar valor atual
        dados[f'Open_PctChange_Lag_{i}'] = dados['Open_PctChange'].shift(i)

        # High e Low: disponíveis durante o dia, usar valor atual
        dados[f'High_PctChange_Lag_{i}'] = dados['High_PctChange'].shift(i)
        dados[f'Low_PctChange_Lag_{i}'] = dados['Low_PctChange'].shift(i)

        # Close: usar versão histórica para lags (trading realístico)
        dados[f'Close_PctChange_Lag_{i}'] = dados['Close_PctChange_Historical'].shift(i)

    # Preencher NaN das features com zero (primeiras linhas)
    dados['Media_OHLC_PctChange'] = dados['Media_OHLC_PctChange'].fillna(0)
    dados['Open_PctChange'] = dados['Open_PctChange'].fillna(0)
    dados['High_PctChange'] = dados['High_PctChange'].fillna(0)
    dados['Low_PctChange'] = dados['Low_PctChange'].fillna(0)
    dados['Close_PctChange'] = dados['Close_PctChange'].fillna(0)

    for i in range(1, ohlc_lags + 1):
        dados[f'Open_PctChange_Lag_{i}'] = dados[f'Open_PctChange_Lag_{i}'].fillna(0)
        dados[f'High_PctChange_Lag_{i}'] = dados[f'High_PctChange_Lag_{i}'].fillna(0)
        dados[f'Low_PctChange_Lag_{i}'] = dados[f'Low_PctChange_Lag_{i}'].fillna(0)
        dados[f'Close_PctChange_Lag_{i}'] = dados[f'Close_PctChange_Lag_{i}'].fillna(0)

    # ===================== FEATURES DERIVADAS DOS SINAIS BUTTERWORTH =====================
    # Inclui as médias móveis MM10, MM25, MM100 e seus pct_change como features
    for mm in ['MM10', 'MM25', 'MM100']:
        if mm in dados.columns:
            # Variação percentual da MM em relação ao dia anterior
            dados[f'{mm}_PctChange'] = dados[mm].pct_change().fillna(0)
            # Diferença entre a média OHLC e a MM
            dados[f'Diff_OHLC_{mm}'] = (dados['Media_OHLC'] - dados[mm]).fillna(0)
    # Relações entre as médias móveis
    if all(mm in dados.columns for mm in ['MM10', 'MM25', 'MM100']):
        dados['MM10_Menos_MM25'] = (dados['MM10'] - dados['MM25']).fillna(0)
        dados['MM25_Menos_MM100'] = (dados['MM25'] - dados['MM100']).fillna(0)
        dados['MM10_Menos_MM100'] = (dados['MM10'] - dados['MM100']).fillna(0)
    # =====================================================================================

    # Criar features lagged das variáveis econométricas
    # Para features que dependem de Close, usar versões históricas nas lags
    # Para features que não dependem de Close, usar versões atuais nas lags

    # Features que NÃO dependem de Close (usar versão atual)
    features_sem_close = ['Volume', 'Spread', 'Volatilidade', 'Parkinson_Volatility', 'EMV', 'EMV_MA', 'VO', 'High_Max_50', 'Low_Min_50']

    # Features que DEPENDEM de Close (usar versão histórica)
    features_com_close = ['MFI', 'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line']

    # Criar lags para features que não dependem de Close
    for feature in features_sem_close:
        if feature in dados.columns:
            for i in range(1, econometric_lags + 1):
                dados[f'{feature}_Lag_{i}'] = dados[feature].shift(i)

    # Criar lags para features que dependem de Close (usar versões históricas)
    for feature in features_com_close:
        feature_historical = f'{feature}_Historical'
        if feature_historical in dados.columns:
            for i in range(1, econometric_lags + 1):
                dados[f'{feature}_Lag_{i}'] = dados[feature_historical].shift(i)

    # Remover linhas com NaN APENAS do início da série (manter último dia)
    # Como preenchemos Media_OHLC_PctChange com zeros, essas features não serão perdidas
    # IMPORTANTE: NÃO remover último dia que pode ter NaN em Media_OHLC_Futura

    # Identificar colunas essenciais que não podem ter NaN
    colunas_essenciais = ['Media_OHLC', 'Media_OHLC_PctChange', 'Volume', 'Spread', 'Volatilidade']

    # Remover apenas linhas onde colunas essenciais têm NaN
    dados = dados.dropna(subset=colunas_essenciais)

    return dados
