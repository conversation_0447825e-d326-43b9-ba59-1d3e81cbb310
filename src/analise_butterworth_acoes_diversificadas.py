#!/usr/bin/env python3
"""
Script para análise das 20 ações diversificadas usando filtros Butterworth
Análise técnica com filtros Butterworth equivalentes às médias móveis
Adaptado para usar configuração centralizada do config.yaml
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
from scipy.signal import butter, lfilter, lfiltic

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling
from config_loader import config, setup_environment

def carregar_acoes_diversificadas():
    """
    Carrega todas as ações do arquivo CSV de diversificação usando configuração
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('acoes_diversificacao', 'acoes_diversificacao.csv')
        df = pd.read_csv(csv_path)

        # Pegar todas as ações (excluindo linhas vazias)
        acoes = []
        for _, row in df.iterrows():
            if pd.notna(row['Ticker']) and row['Ticker'].strip():
                ticker = row['Ticker'] + '.SA'
                nome = row['Nome']
                acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def carregar_acoes_carteira():
    """
    Carrega as ações da carteira do arquivo CSV usando configuração
    Filtra apenas ações com quantidade líquida > 0 (ainda na carteira)
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Pegar todas as ações da carteira e calcular quantidade líquida
        acoes = []
        tickers_processados = set()

        for _, row in df.iterrows():
            if pd.notna(row['ticker']) and row['ticker'].strip():
                ticker = row['ticker']

                # Evitar duplicatas
                if ticker in tickers_processados:
                    continue
                tickers_processados.add(ticker)

                # Calcular quantidade líquida (compras - vendas)
                quantidade_liquida = obter_quantidade_carteira(ticker)

                # Incluir apenas ações com quantidade > 0 (ainda na carteira)
                if quantidade_liquida > 0:
                    # Extrair nome da empresa do ticker (simplificado)
                    nome = ticker.replace('.SA', '')
                    acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações ATIVAS da carteira do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo da carteira: {e}")
        return []

def obter_quantidade_carteira(ticker):
    """
    Obtém a quantidade líquida de ações de um ticker na carteira usando configuração
    Valores negativos na quantidade indicam vendas
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Filtrar por ticker e somar quantidades (compras - vendas)
        ticker_data = df[df['ticker'] == ticker]
        if not ticker_data.empty:
            quantidade_liquida = ticker_data['quantidade'].sum()
            return quantidade_liquida
        else:
            return 0

    except Exception as e:
        print(f"❌ Erro ao obter quantidade da carteira para {ticker}: {e}")
        return 0

def limpar_figuras_antigas():
    """
    Remove todas as figuras antigas e arquivos CSV individuais dos diretórios de análise
    usando configurações do YAML
    """
    import glob

    # Usar configuração para obter diretórios
    output_dirs = config.get_output_directories()

    diretorios_limpeza = [
        output_dirs.get('figures', 'results/figures') + '/butterworth_analysis',
        output_dirs.get('butterworth_analysis', 'results/csv/butterworth_analysis') + '/individual_stocks'
    ]

    print("🧹 Limpando figuras e arquivos CSV antigos...")

    for diretorio in diretorios_limpeza:
        if os.path.exists(diretorio):
            try:
                # Remover todos os arquivos do diretório
                arquivos_removidos = 0
                for arquivo in glob.glob(os.path.join(diretorio, '*')):
                    if os.path.isfile(arquivo):
                        os.remove(arquivo)
                        print(f"   🗑️  Removido: {arquivo}")
                        arquivos_removidos += 1
                print(f"   ✅ Diretório limpo: {diretorio} ({arquivos_removidos} arquivos removidos)")
            except Exception as e:
                print(f"   ⚠️  Erro ao limpar {diretorio}: {e}")
        else:
            print(f"   📁 Diretório não existe: {diretorio}")

    print("✅ Limpeza concluída!")

def corrigir_valores_tims3(dados, ticker):
    """
    Corrige valores da TIMS3 até 02/07/2025 dividindo por 100

    Args:
        dados: DataFrame com dados históricos
        ticker: Símbolo da ação

    Returns:
        DataFrame com valores corrigidos
    """
    if 'TIMS3' not in ticker:
        return dados

    # Data limite para correção (sem timezone)
    data_limite = pd.Timestamp('2025-07-02').tz_localize(None)

    # Colunas OHLC e Volume para corrigir
    colunas_preco = ['Open', 'High', 'Low', 'Close']

    # Converter índice para timezone naive se necessário
    if dados.index.tz is not None:
        dados_index = dados.index.tz_localize(None)
    else:
        dados_index = dados.index

    # Filtrar dados até a data limite
    mask_correcao = dados_index <= data_limite

    if mask_correcao.any():
        print(f"     🔧 Corrigindo valores TIMS3 até 02/07/2025 (dividindo por 100)")

        # Aplicar correção nas colunas de preço
        for coluna in colunas_preco:
            if coluna in dados.columns:
                dados.loc[mask_correcao, coluna] = dados.loc[mask_correcao, coluna] / 100

        print(f"     ✅ Correção aplicada em {mask_correcao.sum()} registros")

    return dados

def corrigir_valores_zero_ultimo_dia(dados):
    """
    Corrige valores zero no último dia substituindo pelo valor do dia anterior

    Args:
        dados: DataFrame com dados históricos

    Returns:
        DataFrame com valores corrigidos
    """
    if len(dados) < 2:
        return dados

    # Colunas OHLC para verificar
    colunas_ohlc = ['Open', 'High', 'Low', 'Close']

    # Verificar e corrigir valores zero no último dia
    for coluna in colunas_ohlc:
        if coluna in dados.columns:
            ultimo_valor = dados[coluna].iloc[-1]
            if ultimo_valor == 0 or pd.isna(ultimo_valor):
                # Substituir pelo valor do penúltimo dia
                penultimo_valor = dados['Close'].iloc[-2]
                if not pd.isna(penultimo_valor) and penultimo_valor != 0:
                    dados.loc[dados.index[-1], coluna] = penultimo_valor
                    print(f"     ⚠️  Corrigido {coluna} do último dia: 0 → {penultimo_valor:.2f}")

    return dados

def calcular_sinal_filtrado(dados):
    """
    Calcula médias móveis de 10, 25, 100 e 200 dias baseadas na média OHLC

    Args:
        dados: DataFrame com dados históricos

    Returns:
        DataFrame com médias móveis adicionadas
    """
    # Corrigir valores zero no último dia
    dados = corrigir_valores_zero_ultimo_dia(dados)

    # Calcular média inteligente:
    # - Dias anteriores: usar OHLC completo (Close disponível)
    # - Dia atual: usar OHL (para trading intraday)
    dados['Media_OHLC'] = np.abs((dados['Open'] + dados['Close'] + dados['Low'] + dados['High'])/4)

    # Para o último dia (dia atual), usar média OHL se Close não estiver disponível
    # ou se for para trading intraday
    ultimo_indice = len(dados) - 1
    close_ultimo_dia = dados['Close'].iloc[ultimo_indice]

    # Verificar se é Series e extrair valor escalar
    if hasattr(close_ultimo_dia, 'item'):
        close_valor = close_ultimo_dia.item()
    else:
        close_valor = close_ultimo_dia

    if pd.isna(close_valor) or close_valor == 0:
        dados['Media_OHLC'].iloc[ultimo_indice] = np.abs((
            dados['Open'].iloc[ultimo_indice] +
            dados['High'].iloc[ultimo_indice] +
            dados['Low'].iloc[ultimo_indice]
        ) / 3)

    # Obter configurações do filtro Butterworth - APENAS do config.yaml
    butterworth_config = config.get_butterworth_config()
    filter_order = butterworth_config['filter_order']  # Sem fallback - deve estar no YAML
    cutoff_frequencies = butterworth_config['cutoff_frequencies']  # Sem fallback - deve estar no YAML
    filter_type = butterworth_config['filter_type']  # Sem fallback - deve estar no YAML
    analog = butterworth_config['analog']  # Sem fallback - deve estar no YAML

    # MM10 - Filtro mais rápido (frequência de corte mais alta)
    cutoff_mm10 = cutoff_frequencies['mm10']  # Sem fallback - deve estar no YAML
    b, a = butter(filter_order, cutoff_mm10, btype=filter_type, analog=analog)
    zi = lfiltic(b, a, dados['Media_OHLC'].iloc[:len(b)].values, dados['Media_OHLC'].iloc[:len(a)].values)
    filtered_signal, _ = lfilter(b, a, dados['Media_OHLC'], zi=zi)
    dados['MM10'] = filtered_signal

    # MM25 - Filtro intermediário
    cutoff_mm25 = cutoff_frequencies['mm25']  # Sem fallback - deve estar no YAML
    b, a = butter(filter_order, cutoff_mm25, btype=filter_type, analog=analog)
    zi = lfiltic(b, a, dados['Media_OHLC'].iloc[:len(b)].values, dados['Media_OHLC'].iloc[:len(a)].values)
    filtered_signal, _ = lfilter(b, a, dados['Media_OHLC'], zi=zi)
    dados['MM25'] = filtered_signal

    # MM100 - Filtro lento
    cutoff_mm100 = cutoff_frequencies['mm100']  # Sem fallback - deve estar no YAML
    b, a = butter(filter_order, cutoff_mm100, btype=filter_type, analog=analog)
    zi = lfiltic(b, a, dados['Media_OHLC'].iloc[:len(b)].values, dados['Media_OHLC'].iloc[:len(a)].values)
    filtered_signal, _ = lfilter(b, a, dados['Media_OHLC'], zi=zi)
    dados['MM100'] = filtered_signal

    # MM200 - Filtro mais lento (frequência de corte mais baixa)
    cutoff_mm200 = cutoff_frequencies['mm200']  # Sem fallback - deve estar no YAML
    b, a = butter(filter_order, cutoff_mm200, btype=filter_type, analog=analog)
    zi = lfiltic(b, a, dados['Media_OHLC'].iloc[:len(b)].values, dados['Media_OHLC'].iloc[:len(a)].values)
    filtered_signal, _ = lfilter(b, a, dados['Media_OHLC'], zi=zi)
    dados['MM200'] = filtered_signal
    





    return dados

def obter_dados_com_butterworth(ticker, nome):
    """
    Obtém dados e calcula médias móveis usando filtro Butterworth
    Usa configurações do config.yaml para parâmetros

    Args:
        ticker: Símbolo da ação
        nome: Nome da empresa
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")

        stock = yf.Ticker(ticker)

        # Usar configuração para período de dados - APENAS do config.yaml
        data_config = config.get_data_config()
        period = data_config['periods']['default_period']  # Sem fallback - deve estar no YAML
        dados = stock.history(period=period)

        if dados.empty or len(dados) < 200:  # Mínimo para calcular MM200
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None

        print(f"     📅 Dados obtidos: {len(dados)} dias")

        # # Aplicar correção para TIMS3 se necessário
        # dados = corrigir_valores_tims3(dados, ticker)

        # Calcular médias móveis usando filtro Butterworth
        dados = calcular_sinal_filtrado(dados)

        # Calcular spread usando configurações - APENAS do config.yaml
        spread_config = config.get_spread_config()
        spread_window = spread_config['window']  # Sem fallback - deve estar no YAML

        try:
            spread_estimado = edge_rolling(dados[['Open', 'High', 'Low', 'Close']], window=spread_window)
            if spread_config['convert_to_percentage']:  # Sem fallback - deve estar no YAML
                dados['Spread'] = spread_estimado * spread_config['percentage_multiplier']  # Sem fallback
            else:
                dados['Spread'] = spread_estimado
        except Exception as e:
            print(f"     ⚠️ Erro ao calcular spread: {e}")
            # Fallback usando configuração - APENAS do config.yaml
            if spread_config['use_volatility_fallback']:  # Sem fallback - deve estar no YAML
                returns = dados['Media_OHLC'].pct_change().dropna()
                volatility_window = spread_config['volatility_window']  # Sem fallback - deve estar no YAML
                volatilidade = returns.rolling(window=volatility_window).std()
                dados['Spread'] = volatilidade * 100

        # Preencher NaN no spread
        dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())

        # Calcular tendência como diferença entre médias OHLC consecutivas
        dados['Tendencia'] = dados['Media_OHLC'].diff()

        # Pegar últimos 12 meses dos dados
        dados_12m = dados.tail(252)

        print(f"     ✅ {len(dados_12m)} dias (com filtros Butterworth MM10, MM25, MM100 e MM200)")

        return dados_12m

    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def analisar_tendencia_butterworth(dados):
    """
    Analisa a tendência baseada nas médias móveis tradicionais
    """
    if dados is None or len(dados) < 50:
        return "Indefinida", "gray"

    preco_atual = dados['Media_OHLC'].iloc[-1]
    butterworth10_atual = dados['MM10'].iloc[-1]
    butterworth25_atual = dados['MM25'].iloc[-1]
    butterworth100_atual = dados['MM100'].iloc[-1]
    butterworth200_atual = dados['MM200'].iloc[-1]

    # Verificar se os valores estão válidos
    if pd.isna(butterworth10_atual) or pd.isna(butterworth25_atual) or pd.isna(butterworth100_atual) or pd.isna(butterworth200_atual):
        return "Indefinida", "gray"

    # Análise de tendência baseada em médias móveis tradicionais
    if preco_atual > butterworth10_atual > butterworth25_atual > butterworth100_atual > butterworth200_atual:
        return "Altista Forte", "darkgreen"
    elif preco_atual > butterworth10_atual > butterworth25_atual > butterworth100_atual > butterworth200_atual:
        return "Altista Moderada", "green"
    elif preco_atual < butterworth10_atual < butterworth25_atual < butterworth100_atual < butterworth200_atual:
        return "Baixista Forte", "darkred"
    elif preco_atual < butterworth10_atual < butterworth25_atual < butterworth100_atual < butterworth200_atual:
        return "Baixista Moderada", "red"
    else:
        return "Lateral", "orange"

def criar_grafico_butterworth(ticker, nome, dados):
    """
    Cria gráfico com média OHLC e médias móveis tradicionais
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    # Calcular estatísticas
    preco_inicial = dados['Media_OHLC'].iloc[0]
    preco_final = dados['Media_OHLC'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100

    # Análise de tendência
    tendencia, cor_tendencia = analisar_tendencia_butterworth(dados)

    # Gráfico 1: Média OHLC + Médias Móveis Tradicionais
    ax1.plot(dados.index, dados['Media_OHLC'], linewidth=2.5, color="#eb1414",
             label='Média OHLC', zorder=3)

    # Médias móveis tradicionais
    ax1.plot(dados.index, dados['MM10'], linewidth=5, color='blue',
             label='MM 10 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM25'], linewidth=2, color='green',
             label='MM 25 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM100'], linewidth=2, color='purple',
             label='MM 100 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM200'], linewidth=2, color='magenta', linestyle='--',
             label='MM 200 dias', alpha=0.8, zorder=2)


    # Área entre preço e MM200 para visualizar posicionamento
    ax1.fill_between(dados.index, dados['Media_OHLC'], dados['MM200'],
                     where=(dados['Media_OHLC'] >= dados['MM200']),
                     color='lightgreen', alpha=0.3, interpolate=True,
                     label='Preço > MM200')
    ax1.fill_between(dados.index, dados['Media_OHLC'], dados['MM200'],
                     where=(dados['Media_OHLC'] < dados['MM200']),
                     color='lightcoral', alpha=0.3, interpolate=True,
                     label='Preço < MM200')

    # Título do gráfico - incluir quantidade líquida de ações da carteira
    quantidade_carteira = obter_quantidade_carteira(ticker)
    if quantidade_carteira > 0:
        titulo_base = f'{nome} ({ticker.replace(".SA", "")}) - Filtros Butterworth - {quantidade_carteira} ações na carteira'
    elif quantidade_carteira < 0:
        titulo_base = f'{nome} ({ticker.replace(".SA", "")}) - Filtros Butterworth - POSIÇÃO VENDIDA ({abs(quantidade_carteira)} ações)'
    else:
        titulo_base = f'{nome} ({ticker.replace(".SA", "")}) - Filtros Butterworth - SEM POSIÇÃO'

    ax1.set_title(titulo_base, fontsize=16, fontweight='bold')
    ax1.set_ylabel('Valor (R$)', fontsize=12)

    # Posicionar legenda no canto superior esquerdo
    ax1.legend(loc='upper left', fontsize=9, ncol=2, framealpha=0.9,
              fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3)

    # Estatísticas no gráfico - posicionar no canto inferior esquerdo
    butterworth10_atual = dados['MM10'].iloc[-1] if not pd.isna(dados['MM10'].iloc[-1]) else 0
    butterworth25_atual = dados['MM25'].iloc[-1] if not pd.isna(dados['MM25'].iloc[-1]) else 0
    butterworth100_atual = dados['MM100'].iloc[-1] if not pd.isna(dados['MM100'].iloc[-1]) else 0
    butterworth200_atual = dados['MM200'].iloc[-1] if not pd.isna(dados['MM200'].iloc[-1]) else 0

    stats_text = f'Média OHLC: R$ {preco_final:.2f} | Performance: {performance:+.1f}%\n'
    stats_text += f'MM10: R$ {butterworth10_atual:.2f} | MM25: R$ {butterworth25_atual:.2f}\n'
    stats_text += f'MM100: R$ {butterworth100_atual:.2f} | MM200: R$ {butterworth200_atual:.2f}\n'
    stats_text += f'Tendência: {tendencia}'

    # Posicionar no canto inferior esquerdo para não sobrepor dados
    ax1.text(0.02, 0.02, stats_text, transform=ax1.transAxes,
             fontsize=10, verticalalignment='bottom',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.95,
                      edgecolor=cor_tendencia, linewidth=2))

    # Gráfico 2: Spread e Tendência
    if 'Spread' in dados.columns and 'Tendencia' in dados.columns:
        

        # Spread no eixo esquerdo
        line1 = ax2.plot(dados.index, dados['Volume'], alpha=0.7, color='purple',
                        linewidth=1.5, label='Volume')
        ax2.set_ylabel('Volume', fontsize=12, color='purple')
        ax2.tick_params(axis='y', labelcolor='purple')

        

        ax2.set_title('Volume', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)

        # Combinar legendas - posicionar no canto superior esquerdo
        lines = line1
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left', framealpha=0.9,
                  fancybox=True, shadow=True)

    ax2.set_xlabel('Data', fontsize=12)

    plt.tight_layout()

    # Ensure directory exists
    os.makedirs('results/figures/butterworth_analysis', exist_ok=True)
    os.makedirs('results/csv/butterworth_analysis/individual_stocks', exist_ok=True)

    # Salvar gráfico
    nome_arquivo = f"results/figures/butterworth_analysis/butterworth_{ticker.replace('.SA', '')}_12m.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

    # Salvar dados de séries temporais em CSV
    ticker_clean = ticker.replace('.SA', '')
    csv_dados = dados[['Media_OHLC', 'MM10', 'MM25', 'MM100', 'MM200', 'Spread', 'Tendencia']].copy()
    csv_dados.index.name = 'Data'

    # Renomear colunas para melhor clareza
    csv_dados.columns = [
        'Preco_Media_OHLC',
        'Butterworth_10_dias',
        'Butterworth_25_dias',
        'Butterworth_100_dias',
        'Butterworth_200_dias',
        'Spread_Percent',
        'Tendencia_Diaria'
    ]

    csv_filename = f"results/csv/butterworth_analysis/individual_stocks/butterworth_series_{ticker_clean}.csv"
    csv_dados.to_csv(csv_filename)

    print(f"     💾 {nome_arquivo}")
    print(f"     📊 {csv_filename}")

    resultado = {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_atual': preco_final,  # Média OHLC
        'butterworth10': butterworth10_atual,
        'butterworth25': butterworth25_atual,
        'butterworth100': butterworth100_atual,
        'butterworth200': butterworth200_atual,
        'tendencia': tendencia,
        'cor_tendencia': cor_tendencia
    }

    return resultado

def gerar_relatorio_butterworth(resultados):
    """
    Gera relatório detalhado da análise com médias móveis
    """
    print("\n" + "="*140)
    print("📊 RELATÓRIO ANÁLISE TÉCNICA - MÉDIAS MÓVEIS (10, 25, 50, 100, 200 DIAS)")
    print("="*140)

    # Ordenar por performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)

    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'Perf%':<8} {'Média OHLC':<12} {'MM10':<8} {'MM25':<8} {'MM100':<8} {'MM200':<8} {'Tendência':<15}")
    print("-" * 130)

    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['performance'] >= 0 else "🔴"

        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:19]:<20} {r['performance']:>+6.1f}% "
              f"{r['preco_atual']:>10.2f} {r['butterworth10']:>6.2f} {r['butterworth25']:>6.2f} {r['butterworth100']:>6.2f} {r['butterworth200']:>6.2f} "
              f"{r['tendencia']:<15}")

    # Estatísticas por tendência
    print("\n" + "="*120)
    print("📊 ESTATÍSTICAS POR TENDÊNCIA")
    print("="*120)

    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r['performance'])

    for tendencia, performances in tendencias.items():
        count = len(performances)
        media = np.mean(performances)
        print(f"{tendencia:<20}: {count:2d} ações | Performance média: {media:+6.1f}%")

    # Análise de posicionamento
    print("\n" + "="*140)
    print("🎯 ANÁLISE DE POSICIONAMENTO")
    print("="*140)

    acima_butterworth10 = len([r for r in resultados if r['preco_atual'] > r['butterworth10']])
    acima_butterworth25 = len([r for r in resultados if r['preco_atual'] > r['butterworth25']])
    acima_butterworth100 = len([r for r in resultados if r['preco_atual'] > r['butterworth100']])
    acima_butterworth200 = len([r for r in resultados if r['preco_atual'] > r['butterworth200']])
    butterworth10_acima_butterworth25 = len([r for r in resultados if r['butterworth10'] > r['butterworth25']])
    butterworth25_acima_butterworth100 = len([r for r in resultados if r['butterworth25'] > r['butterworth100']])
    butterworth100_acima_butterworth200 = len([r for r in resultados if r['butterworth100'] > r['butterworth200']])

    print(f"Ações com Média OHLC acima da MM10:   {acima_butterworth10:2d}/{len(resultados)} ({acima_butterworth10/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM25:   {acima_butterworth25:2d}/{len(resultados)} ({acima_butterworth25/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM100:  {acima_butterworth100:2d}/{len(resultados)} ({acima_butterworth100/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM200:  {acima_butterworth200:2d}/{len(resultados)} ({acima_butterworth200/len(resultados)*100:.1f}%)")
    print(f"Ações com MM10 > MM25:               {butterworth10_acima_butterworth25:2d}/{len(resultados)} ({butterworth10_acima_butterworth25/len(resultados)*100:.1f}%)")
    print(f"Ações com MM25 > MM100:              {butterworth25_acima_butterworth100:2d}/{len(resultados)} ({butterworth25_acima_butterworth100/len(resultados)*100:.1f}%)")
    print(f"Ações com MM100 > MM200:             {butterworth100_acima_butterworth200:2d}/{len(resultados)} ({butterworth100_acima_butterworth200/len(resultados)*100:.1f}%)")

    # Análise de alinhamento das médias móveis
    print(f"\n📈 ANÁLISE DE ALINHAMENTO DAS MÉDIAS MÓVEIS")
    print("="*140)

    alinhamento_altista = len([r for r in resultados if r['butterworth10'] > r['butterworth25'] > r['butterworth100'] > r['butterworth200']])
    alinhamento_baixista = len([r for r in resultados if r['butterworth10'] < r['butterworth25'] < r['butterworth100'] < r['butterworth200']])

    print(f"Alinhamento Altista (MM10 > MM25 > MM100 > MM200): {alinhamento_altista:2d}/{len(resultados)} ({alinhamento_altista/len(resultados)*100:.1f}%)")
    print(f"Alinhamento Baixista (MM10 < MM25 < MM100 < MM200): {alinhamento_baixista:2d}/{len(resultados)} ({alinhamento_baixista/len(resultados)*100:.1f}%)")
    print(f"Sem alinhamento claro:                             {len(resultados) - alinhamento_altista - alinhamento_baixista:2d}/{len(resultados)} ({(len(resultados) - alinhamento_altista - alinhamento_baixista)/len(resultados)*100:.1f}%)")

def detectar_sinais_trading_butterworth(dados, ticker, acoes_carteira):
    """
    Detecta sinais de trading baseados no cruzamento dos filtros Butterworth
    Retorna sinais apenas para o último dia disponível
    - Sinais de COMPRA: para todas as ações
    - Sinais de VENDA: apenas para ações da carteira
    """
    if len(dados) < 2:
        return None

    # Pegar os dois últimos dias com dados válidos
    dados_validos = dados.dropna(subset=['MM10', 'MM25', 'MM100', 'MM200']).tail(2)

    if len(dados_validos) < 2:
        return None

    ultimo_dia = dados_validos.iloc[-1]
    penultimo_dia = dados_validos.iloc[-2]

    sinais_compra = []
    sinais_venda = []

    # Criar set de tickers da carteira para verificação rápida
    tickers_carteira = {ticker_cart for ticker_cart, _ in acoes_carteira}

    # Verificar cruzamentos da MM10 com outras médias
    medias_outras = ['MM25', 'MM100', 'MM200']

    for media in medias_outras:
        # Cruzamento de baixo para cima = COMPRA (sinal de alta) - para todas as ações
        if (penultimo_dia['MM10'] < penultimo_dia[media] and
            ultimo_dia['MM10'] > ultimo_dia[media]):
            sinais_compra.append(f"Butterworth 10 dias cruzou {media} para cima")

        # Cruzamento de cima para baixo = VENDA (sinal de baixa) - apenas para ações da carteira
        elif (penultimo_dia['MM10'] > penultimo_dia[media] and
              ultimo_dia['MM10'] < ultimo_dia[media] and
              ticker in tickers_carteira):
            sinais_venda.append(f"Butterworth 10 dias cruzou {media} para baixo")

    if sinais_compra or sinais_venda:
        return {
            'data': ultimo_dia.name,
            'preco': ultimo_dia['Media_OHLC'],
            'sinais_compra': sinais_compra,
            'sinais_venda': sinais_venda,
            'total_compra': len(sinais_compra),
            'total_venda': len(sinais_venda)
        }

    return None

def exibir_sinais_trading_butterworth(sinais_trading, acoes_carteira):
    """
    Exibe os sinais de trading detectados no final da execução
    """
    if not sinais_trading:
        print("\n📊 ESTRATÉGIA DE TRADING - FILTROS BUTTERWORTH")
        print("="*60)
        print("✅ Nenhum sinal de compra ou venda detectado para o último dia.")
        return

    print("\n📊 ESTRATÉGIA DE TRADING - FILTROS BUTTERWORTH")
    print("="*60)
    print("🎯 Sinais detectados para o último dia disponível:")

    # Separar sinais de compra e venda
    sinais_compra = [s for s in sinais_trading if s['total_compra'] > 0]
    sinais_venda = [s for s in sinais_trading if s['total_venda'] > 0]

    # Criar set de tickers da carteira para verificação
    tickers_carteira = {ticker for ticker, _ in acoes_carteira}

    # Exibir sinais de compra
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        for sinal in sorted(sinais_compra, key=lambda x: x['total_compra'], reverse=True):
            ticker_clean = sinal['ticker'].replace('.SA', '')
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y')
            print(f"   📈 {ticker_clean} ({sinal['nome'][:30]})")
            print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
            print(f"      📅 Data: {data_str}")
            print(f"      🎯 Sinais: {sinal['total_compra']} cruzamento(s)")
            for detalhe in sinal['sinais_compra']:
                print(f"         • {detalhe}")
            print()

    # Exibir sinais de venda
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações):")
        print("-" * 60)

        # Separar ações da carteira das demais
        vendas_carteira = [s for s in sinais_venda if s['ticker'] in tickers_carteira]
        vendas_outras = [s for s in sinais_venda if s['ticker'] not in tickers_carteira]

        if vendas_carteira:
            print("   🎯 AÇÕES DA SUA CARTEIRA:")
            for sinal in sorted(vendas_carteira, key=lambda x: x['total_venda'], reverse=True):
                ticker_clean = sinal['ticker'].replace('.SA', '')
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y')
                print(f"   📉 {ticker_clean} ({sinal['nome'][:30]}) ⚠️ VOCÊ POSSUI")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      🎯 Sinais: {sinal['total_venda']} cruzamento(s)")
                for detalhe in sinal['sinais_venda']:
                    print(f"         • {detalhe}")
                print()

        if vendas_outras:
            print("   📊 OUTRAS AÇÕES:")
            for sinal in sorted(vendas_outras, key=lambda x: x['total_venda'], reverse=True):
                ticker_clean = sinal['ticker'].replace('.SA', '')
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y')
                print(f"   📉 {ticker_clean} ({sinal['nome'][:30]})")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      🎯 Sinais: {sinal['total_venda']} cruzamento(s)")
                for detalhe in sinal['sinais_venda']:
                    print(f"         • {detalhe}")
                print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🟢 Compra: {len(sinais_compra)} ações")
    print(f"   🔴 Venda: {len(sinais_venda)} ações")
    if sinais_venda:
        vendas_carteira = [s for s in sinais_venda if s['ticker'] in tickers_carteira]
        if vendas_carteira:
            print(f"   ⚠️  Vendas na sua carteira: {len(vendas_carteira)} ações")
    print(f"   📊 Total de sinais: {len(sinais_trading)} ações")

def main():
    print("📊 ANÁLISE COM FILTROS BUTTERWORTH - AÇÕES DIVERSIFICADAS E CARTEIRA")
    print("="*80)

    # Configurar ambiente automaticamente
    setup_environment()

    # Mostrar configurações sendo usadas - APENAS do config.yaml
    butterworth_config = config.get_butterworth_config()
    print("📊 Configurações carregadas:")
    print(f"   • Ordem do filtro: {butterworth_config['filter_order']}")  # Sem fallback
    print(f"   • Frequências de corte: {butterworth_config['cutoff_frequencies']}")  # Sem fallback
    print(f"   • Usar média OHLC: {config.get('moving_averages.use_ohlc_average')}")  # Sem fallback
    print(f"   • Período de dados: {config.get('data.periods.default_period')}")  # Sem fallback

    print("\n📊 Análise Técnica com:")
    print("   • Média OHLC (Open + Close + Low + High) / 4")
    print("   • Filtro Butterworth 10 dias (MM10) baseado na Média OHLC")
    print("   • Filtro Butterworth 25 dias (MM25) baseado na Média OHLC")
    print("   • Filtro Butterworth 100 dias (MM100) baseado na Média OHLC")
    print("   • Filtro Butterworth 200 dias (MM200) baseado na Média OHLC")
    print("   • Análise de tendência baseada no alinhamento dos filtros")
    print("   • Spread Bid/Ask e tendência diária")
    print("   • Estratégia de Trading: Cruzamento de Filtros Butterworth")

    # Limpar figuras antigas antes de começar
    limpar_figuras_antigas()

    # Carregar ações dos dois arquivos CSV
    acoes_diversificadas = carregar_acoes_diversificadas()
    acoes_carteira = carregar_acoes_carteira()

    # Combinar todas as ações, evitando duplicatas
    todas_acoes = []
    tickers_vistos = set()

    # Adicionar ações diversificadas
    for ticker, nome in acoes_diversificadas:
        if ticker not in tickers_vistos:
            todas_acoes.append((ticker, nome, "Diversificada"))
            tickers_vistos.add(ticker)

    # Adicionar ações da carteira
    for ticker, nome in acoes_carteira:
        if ticker not in tickers_vistos:
            todas_acoes.append((ticker, nome, "Carteira"))
            tickers_vistos.add(ticker)
        else:
            # Marcar como ambas se já existe
            for i, (t, n, origem) in enumerate(todas_acoes):
                if t == ticker:
                    todas_acoes[i] = (t, n, "Diversificada + Carteira")
                    break

    if not todas_acoes:
        print("❌ Não foi possível carregar ações dos arquivos CSV")
        return

    print(f"\n📋 Serão analisadas {len(todas_acoes)} ações no total:")
    print(f"   • Ações diversificadas: {len(acoes_diversificadas)}")
    print(f"   • Ações da carteira: {len(acoes_carteira)}")
    print(f"   • Total único: {len(todas_acoes)}")

    print(f"\n📊 Iniciando análise com filtros Butterworth...")

    resultados = []
    sinais_trading = []
    sucesso = 0
    erro = 0

    for i, (ticker, nome, origem) in enumerate(todas_acoes, 1):
        print(f"\n[{i:2d}/{len(todas_acoes)}] ({origem})", end=" ")

        dados = obter_dados_com_butterworth(ticker, nome)

        if dados is not None:
            resultado = criar_grafico_butterworth(ticker, nome, dados)
            resultado['origem'] = origem  # Adicionar origem ao resultado
            resultados.append(resultado)

            # Detectar sinais de trading
            sinal = detectar_sinais_trading_butterworth(dados, ticker, acoes_carteira)
            if sinal:
                sinal['ticker'] = ticker
                sinal['nome'] = nome
                sinal['origem'] = origem
                sinais_trading.append(sinal)

            sucesso += 1
        else:
            erro += 1

    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")

    if resultados:
        # Gerar relatório
        gerar_relatorio_butterworth(resultados)

        # Salvar resultados em CSV usando configurações
        try:
            df_resultados = pd.DataFrame(resultados)
            output_dirs = config.get_output_directories()
            butterworth_dir = output_dirs.get('butterworth_analysis', 'results/csv/butterworth_analysis')
            os.makedirs(butterworth_dir, exist_ok=True)
            csv_path = f"{butterworth_dir}/resultados_butterworth_completo.csv"
            df_resultados.to_csv(csv_path, index=False)
            print(f"   • Resultados resumo salvos em: {csv_path}")
            print(f"   • Séries temporais individuais salvas em: {butterworth_dir}/individual_stocks/")
        except Exception as e:
            print(f"   ⚠️ Erro ao salvar CSV: {e}")

        # Exibir sinais de trading no final
        exibir_sinais_trading_butterworth(sinais_trading, acoes_carteira)

    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
