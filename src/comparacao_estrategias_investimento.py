#!/usr/bin/env python3
"""
Script para comparar estratégias de investimento usando MM, Butterworth e XGBoost
Simula investimento usando sinais das 3 metodologias com parâmetros configuráveis
Implementa método bootstrap com 10 cenários sorteando 10 ações cada
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import sys
from datetime import datetime, timedelta
import warnings
import random
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import ConfigLoader
from config_loader import config, setup_environment

def carregar_acoes_disponiveis():
    """
    Carrega todas as ações disponíveis do arquivo de diversificação
    """
    try:
        file_paths = config.get_file_paths()
        csv_path = file_paths['acoes_diversificacao']
        df = pd.read_csv(csv_path)

        # Pegar todas as ações (excluindo linhas vazias)
        acoes = []
        for _, row in df.iterrows():
            if pd.notna(row['Ticker']) and row['Ticker'].strip():
                ticker = row['Ticker'] + '.SA'
                nome = row['Nome']
                acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações disponíveis do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def sortear_acoes_cenario(acoes_disponiveis, num_acoes=10, seed=None):
    """
    Sorteia um subconjunto de ações para um cenário

    Args:
        acoes_disponiveis: Lista de todas as ações disponíveis
        num_acoes: Número de ações para sortear
        seed: Seed para reprodutibilidade

    Returns:
        Lista de ações sorteadas
    """
    if seed is not None:
        random.seed(seed)

    # Garantir que não sorteamos mais ações do que temos disponível
    num_acoes = min(num_acoes, len(acoes_disponiveis))

    acoes_sorteadas = random.sample(acoes_disponiveis, num_acoes)
    tickers_sorteados = [ticker for ticker, _ in acoes_sorteadas]

    print(f"   🎲 Ações sorteadas: {', '.join([ticker.replace('.SA', '') for ticker in tickers_sorteados])}")

    return acoes_sorteadas

def carregar_dados_mm(acoes_filtro=None):
    """
    Carrega dados históricos das médias móveis dos arquivos individuais

    Args:
        acoes_filtro: Lista de tickers para filtrar (opcional)
    """
    try:
        individual_dir = 'results/csv/mm_analysis/individual_stocks'
        if not os.path.exists(individual_dir):
            print(f"❌ Diretório MM não encontrado: {individual_dir}")
            return None

        # Listar arquivos individuais
        arquivos = [f for f in os.listdir(individual_dir) if f.startswith('mm_series_') and f.endswith('.csv')]

        if not arquivos:
            print(f"❌ Nenhum arquivo MM encontrado em: {individual_dir}")
            return None

        dados_completos = []

        for arquivo in arquivos:
            ticker = arquivo.replace('mm_series_', '').replace('.csv', '')

            # Filtrar por ações específicas se fornecido
            if acoes_filtro is not None:
                ticker_completo = ticker + '.SA'
                if ticker_completo not in acoes_filtro:
                    continue

            caminho_arquivo = os.path.join(individual_dir, arquivo)

            try:
                df_acao = pd.read_csv(caminho_arquivo)
                df_acao['Data'] = pd.to_datetime(df_acao['Data'])
                df_acao['Ticker'] = ticker

                # Criar sinais baseados nas médias móveis
                # Sinal de compra: preço cruza acima da MM25
                df_acao['Sinal_Compra'] = ((df_acao['Preco_Media_OHLC'] > df_acao['MM25_dias']) &
                                          (df_acao['Preco_Media_OHLC'].shift(1) <= df_acao['MM25_dias'].shift(1))).astype(int)

                # Sinal de venda: preço cruza abaixo da MM25
                df_acao['Sinal_Venda'] = ((df_acao['Preco_Media_OHLC'] < df_acao['MM25_dias']) &
                                         (df_acao['Preco_Media_OHLC'].shift(1) >= df_acao['MM25_dias'].shift(1))).astype(int)

                # Renomear coluna para compatibilidade
                df_acao['Close'] = df_acao['Preco_Media_OHLC']

                dados_completos.append(df_acao)

            except Exception as e:
                print(f"   ⚠️ Erro ao carregar {arquivo}: {e}")

        if dados_completos:
            df_final = pd.concat(dados_completos, ignore_index=True)
            print(f"✅ MM: {len(df_final)} registros carregados de {len(dados_completos)} ações")
            return df_final
        else:
            print(f"❌ Nenhum dado MM válido carregado")
            return None

    except Exception as e:
        print(f"❌ Erro ao carregar dados MM: {e}")
        return None

def carregar_dados_butterworth(acoes_filtro=None):
    """
    Carrega dados históricos do filtro Butterworth dos arquivos individuais

    Args:
        acoes_filtro: Lista de tickers para filtrar (opcional)
    """
    try:
        individual_dir = 'results/csv/butterworth_analysis/individual_stocks'
        if not os.path.exists(individual_dir):
            print(f"❌ Diretório Butterworth não encontrado: {individual_dir}")
            return None

        # Listar arquivos individuais
        arquivos = [f for f in os.listdir(individual_dir) if f.startswith('butterworth_series_') and f.endswith('.csv')]

        if not arquivos:
            print(f"❌ Nenhum arquivo Butterworth encontrado em: {individual_dir}")
            return None

        dados_completos = []

        for arquivo in arquivos:
            ticker = arquivo.replace('butterworth_series_', '').replace('.csv', '')

            # Filtrar por ações específicas se fornecido
            if acoes_filtro is not None:
                ticker_completo = ticker + '.SA'
                if ticker_completo not in acoes_filtro:
                    continue

            caminho_arquivo = os.path.join(individual_dir, arquivo)

            try:
                df_acao = pd.read_csv(caminho_arquivo)
                df_acao['Data'] = pd.to_datetime(df_acao['Data'])
                df_acao['Ticker'] = ticker

                # Criar sinais baseados no filtro Butterworth
                # Sinal de compra: preço cruza acima do Butterworth_25_dias
                df_acao['Sinal_Compra'] = ((df_acao['Preco_Media_OHLC'] > df_acao['Butterworth_25_dias']) &
                                          (df_acao['Preco_Media_OHLC'].shift(1) <= df_acao['Butterworth_25_dias'].shift(1))).astype(int)

                # Sinal de venda: preço cruza abaixo do Butterworth_25_dias
                df_acao['Sinal_Venda'] = ((df_acao['Preco_Media_OHLC'] < df_acao['Butterworth_25_dias']) &
                                         (df_acao['Preco_Media_OHLC'].shift(1) >= df_acao['Butterworth_25_dias'].shift(1))).astype(int)

                # Renomear coluna para compatibilidade
                df_acao['Close'] = df_acao['Preco_Media_OHLC']

                dados_completos.append(df_acao)

            except Exception as e:
                print(f"   ⚠️ Erro ao carregar {arquivo}: {e}")

        if dados_completos:
            df_final = pd.concat(dados_completos, ignore_index=True)
            print(f"✅ Butterworth: {len(df_final)} registros carregados de {len(dados_completos)} ações")
            return df_final
        else:
            print(f"❌ Nenhum dado Butterworth válido carregado")
            return None

    except Exception as e:
        print(f"❌ Erro ao carregar dados Butterworth: {e}")
        return None

def carregar_dados_xgboost(acoes_filtro=None):
    """
    Carrega dados históricos do XGBoost dos arquivos individuais

    Args:
        acoes_filtro: Lista de tickers para filtrar (opcional)
    """
    try:
        individual_dir = 'results/csv/xgboost_analysis/individual_stocks'
        if not os.path.exists(individual_dir):
            print(f"❌ Diretório XGBoost não encontrado: {individual_dir}")
            return None

        # Listar arquivos individuais
        arquivos = [f for f in os.listdir(individual_dir) if f.startswith('xgboost_') and f.endswith('.csv')]

        if not arquivos:
            print(f"❌ Nenhum arquivo XGBoost encontrado em: {individual_dir}")
            return None

        dados_completos = []

        for arquivo in arquivos:
            ticker = arquivo.replace('xgboost_', '').replace('.csv', '')

            # Filtrar por ações específicas se fornecido
            if acoes_filtro is not None:
                ticker_completo = ticker + '.SA'
                if ticker_completo not in acoes_filtro:
                    continue

            caminho_arquivo = os.path.join(individual_dir, arquivo)

            try:
                df_acao = pd.read_csv(caminho_arquivo)
                df_acao['Data'] = pd.to_datetime(df_acao['Data'])
                df_acao['Ticker'] = ticker

                # Verificar se as colunas necessárias existem
                # Priorizar predições binárias do novo modelo XGBoost
                if 'Pred_Compra' in df_acao.columns and 'Pred_Venda' in df_acao.columns:
                    # Usar predições binárias do modelo XGBoost (novo formato)
                    df_acao['Sinal_Compra'] = df_acao['Pred_Compra']
                    df_acao['Sinal_Venda'] = df_acao['Pred_Venda']
                    print(f"   ✅ Usando PREDIÇÕES XGBoost binárias para {ticker}")
                elif 'Pred_Multiclass' in df_acao.columns:
                    # CompPatibilidade com modelo multiclasse antigo (se existir)
                    # Converter predições multiclasse para formato binário
                    df_acao['Sinal_Compra'] = (df_acao['Pred_Multiclass'] == 1).astype(int)
                    df_acao['Sinal_Venda'] = (df_acao['Pred_Multiclass'] == 2).astype(int)
                    print(f"   ✅ Usando PREDIÇÕES XGBoost multiclasse (compatibilidade) para {ticker}")
                elif 'Sinal_Compra' in df_acao.columns and 'Sinal_Venda' in df_acao.columns:
                    # Usar sinais de treinamento como fallback
                    print(f"   ⚠️ Usando sinais de treinamento para {ticker} (predições não disponíveis)")
                else:
                    print(f"   ❌ Nem predições nem sinais encontrados em {arquivo}")
                    continue

                if 'Media_OHLC' in df_acao.columns:
                    # Renomear coluna para compatibilidade com simulação
                    df_acao['Close'] = df_acao['Media_OHLC']
                    dados_completos.append(df_acao)
                else:
                    print(f"   ⚠️ Coluna Media_OHLC não encontrada em {arquivo}")

            except Exception as e:
                print(f"   ⚠️ Erro ao carregar {arquivo}: {e}")

        if dados_completos:
            df_final = pd.concat(dados_completos, ignore_index=True)
            print(f"✅ XGBoost: {len(df_final)} registros carregados de {len(dados_completos)} ações")
            return df_final
        else:
            print(f"❌ Nenhum dado XGBoost válido carregado")
            return None

    except Exception as e:
        print(f"❌ Erro ao carregar dados XGBoost: {e}")
        return None

def filtrar_periodo_simulacao(df, data_inicio=None):
    """
    Filtra dados para período de simulação configurado
    """
    # Carregar configuração
    config_loader = ConfigLoader()
    periodo_config = config_loader.get_simulation_period('comparison')

    # Converter período para dias
    if periodo_config == '1y':
        dias_periodo = 365
    elif periodo_config == '6mo':
        dias_periodo = 180
    elif periodo_config == '1mo':
        dias_periodo = 30
    elif periodo_config == '2y':
        dias_periodo = 730
    else:
        # Padrão: 1 ano
        dias_periodo = 365

    if data_inicio is None:
        # Usar últimos N meses/anos disponíveis
        data_fim = df['Data'].max()
        data_inicio = data_fim - timedelta(days=dias_periodo)
    else:
        data_fim = data_inicio + timedelta(days=dias_periodo)

    df_filtrado = df[(df['Data'] >= data_inicio) & (df['Data'] <= data_fim)].copy()
    print(f"   📅 Período: {data_inicio.strftime('%Y-%m-%d')} a {data_fim.strftime('%Y-%m-%d')}")
    print(f"   📊 Registros no período: {len(df_filtrado)}")
    print(f"   ⏱️ Duração configurada: {periodo_config}")

    return df_filtrado, data_inicio, data_fim

def simular_investimento(df, metodologia, capital_inicial=None):
    """
    Simula investimento seguindo sinais de uma metodologia
    Com sinal de compra: compra quantidade fixa de ações (se capital permitir)
    Com sinal de venda: vende todas as ações disponíveis da ação indicada
    """
    # Carregar configuração se capital não foi especificado
    if capital_inicial is None:
        config_loader = ConfigLoader()
        capital_inicial = config_loader.get_initial_capital('comparison')

    # Carregar quantidade fixa de ações para compra
    config_loader = ConfigLoader()
    quantidade_fixa = config_loader.config.get('simulation', {}).get('trading', {}).get('fixed_quantity_per_stock', 100)

    print(f"\n💰 Simulando investimento - {metodologia}")
    print(f"   💵 Capital inicial: R$ {capital_inicial:.2f}")
    print(f"   📊 Quantidade fixa por compra: {quantidade_fixa} ações")

    # Inicializar variáveis
    capital_disponivel = capital_inicial
    posicoes = {}  # {ticker: {'quantidade': X, 'preco_compra': Y}}
    historico_transacoes = []
    historico_capital = []

    # Determinar colunas de sinais baseado na metodologia
    # XGBoost usa predições (Pred_Compra/Pred_Venda convertidas para Sinal_Compra/Sinal_Venda)
    # MM e Butterworth usam sinais calculados baseados em cruzamentos
    col_compra = 'Sinal_Compra'
    col_venda = 'Sinal_Venda'
    col_preco = 'Close'  # Todas usam Close (XGBoost foi convertido na carga)

    if 'Prob_Compra' in df.columns:
        df = df.sort_values(by=['Data', 'Pred_Venda', 'Prob_Compra'], ascending=[True, False, False])
    else:
        df = df.sort_values(by='Data', ascending=True)
    # Processar sinais dia a dia
    for _, row in df.iterrows():
        ticker = row['Ticker']
        data = row['Data']
        preco = row[col_preco]
        sinal_compra = row[col_compra] if col_compra in row else 0
        sinal_venda = row[col_venda] if col_venda in row else 0

        # Processar sinal de venda primeiro - VENDER TODAS AS AÇÕES DISPONÍVEIS
        if sinal_venda == 1 and ticker in posicoes:
            quantidade = posicoes[ticker]['quantidade']
            valor_venda = quantidade * preco

            capital_disponivel += valor_venda

            historico_transacoes.append({
                'Data': data,
                'Ticker': ticker,
                'Tipo': 'Venda',
                'Quantidade': quantidade,
                'Preco': preco,
                'Valor': valor_venda,
                'Capital_Pos': capital_disponivel
            })

            del posicoes[ticker]

        # Processar sinal de compra - COMPRAR QUANTIDADE FIXA (se capital permitir)
        elif sinal_compra == 1 and ticker not in posicoes:
            # Calcular valor necessário para comprar quantidade fixa
            quantia = quantidade_fixa*1*max((capital_disponivel//1000),1)
            valor_necessario = quantia*preco
            # if valor_necessario > capital_disponivel:
            #     quantia = int(capital_disponivel//preco)
            #     valor_necessario = quantia*preco

            if capital_disponivel >= valor_necessario:  # Verificar se tem dinheiro suficiente
                capital_disponivel -= valor_necessario
                posicoes[ticker] = {
                    'quantidade': quantia,
                    'preco_compra': preco
                }

                historico_transacoes.append({
                    'Data': data,
                    'Ticker': ticker,
                    'Tipo': 'Compra',
                    'Quantidade': quantia,
                    'Preco': preco,
                    'Valor': valor_necessario,
                    'Capital_Pos': capital_disponivel
                })
        
        # Calcular valor total do portfólio
        valor_posicoes = sum(pos['quantidade'] * preco for pos in posicoes.values())
        capital_total = capital_disponivel + valor_posicoes
        
        historico_capital.append({
            'Data': data,
            'Capital_Disponivel': capital_disponivel,
            'Valor_Posicoes': valor_posicoes,
            'Capital_Total': capital_total
        })
    
    # Calcular resultado final
    valor_final_posicoes = 0
    for ticker, pos in posicoes.items():
        # Usar último preço disponível para cada ticker
        ultimo_preco = df[df['Ticker'] == ticker][col_preco].iloc[-1]
        valor_final_posicoes += pos['quantidade'] * ultimo_preco
    
    capital_final = capital_disponivel + valor_final_posicoes
    rendimento_absoluto = capital_final - capital_inicial
    rendimento_percentual = (rendimento_absoluto / capital_inicial) * 100
    
    resultado = {
        'metodologia': metodologia,
        'capital_inicial': capital_inicial,
        'capital_final': capital_final,
        'capital_disponivel_final': capital_disponivel,
        'valor_posicoes_final': valor_final_posicoes,
        'rendimento_absoluto': rendimento_absoluto,
        'rendimento_percentual': rendimento_percentual,
        'num_transacoes': len(historico_transacoes),
        'num_posicoes_finais': len(posicoes),
        'historico_transacoes': historico_transacoes,
        'historico_capital': historico_capital
    }
    
    print(f"   💵 Capital Final: R$ {capital_final:.2f}")
    print(f"   📈 Rendimento: R$ {rendimento_absoluto:.2f} ({rendimento_percentual:.2f}%)")
    print(f"   🔄 Transações: {len(historico_transacoes)}")
    print(f"   📊 Posições finais: {len(posicoes)}")
    
    return resultado

def criar_graficos_comparacao(resultados):
    """
    Cria gráficos comparando as 3 metodologias
    """
    print(f"\n📊 Criando gráficos de comparação...")

    # Carregar configuração para títulos dinâmicos
    config_loader = ConfigLoader()
    capital_inicial = config_loader.get_initial_capital('comparison')
    periodo = config_loader.get_simulation_period('comparison')

    # Configurar estilo
    plt.style.use('default')

    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'Comparação de Estratégias de Investimento - R$ {capital_inicial:.0f} por {periodo}', fontsize=16, fontweight='bold')
    
    metodologias = [r['metodologia'] for r in resultados]
    cores = ['blue', 'green', 'red']
    
    # 1. Capital Final
    capitals_finais = [r['capital_final'] for r in resultados]
    bars1 = axes[0,0].bar(metodologias, capitals_finais, color=cores, alpha=0.7)
    axes[0,0].set_title('Capital Final')
    axes[0,0].set_ylabel('Capital (R$)')
    axes[0,0].axhline(y=capital_inicial, color='black', linestyle='--', alpha=0.5, label='Capital Inicial')
    
    # Adicionar valores nas barras
    for bar, valor in zip(bars1, capitals_finais):
        axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                      f'R$ {valor:.0f}', ha='center', va='bottom', fontweight='bold')
    
    axes[0,0].legend()
    
    # 2. Rendimento Percentual
    rendimentos_pct = [r['rendimento_percentual'] for r in resultados]
    bars2 = axes[0,1].bar(metodologias, rendimentos_pct, color=cores, alpha=0.7)
    axes[0,1].set_title('Rendimento Percentual')
    axes[0,1].set_ylabel('Rendimento (%)')
    axes[0,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # Adicionar valores nas barras
    for bar, valor in zip(bars2, rendimentos_pct):
        axes[0,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + (1 if valor >= 0 else -3),
                      f'{valor:.1f}%', ha='center', va='bottom' if valor >= 0 else 'top', fontweight='bold')
    
    # 3. Número de Transações
    num_transacoes = [r['num_transacoes'] for r in resultados]
    bars3 = axes[1,0].bar(metodologias, num_transacoes, color=cores, alpha=0.7)
    axes[1,0].set_title('Número de Transações')
    axes[1,0].set_ylabel('Transações')
    
    # Adicionar valores nas barras
    for bar, valor in zip(bars3, num_transacoes):
        axes[1,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                      f'{valor}', ha='center', va='bottom', fontweight='bold')
    
    # 4. Evolução do Capital (se disponível)
    if all('historico_capital' in r for r in resultados):
        for i, resultado in enumerate(resultados):
            historico = pd.DataFrame(resultado['historico_capital'])
            if len(historico) > 0:
                axes[1,1].plot(historico['Data'], historico['Capital_Total'], 
                             label=resultado['metodologia'], color=cores[i], linewidth=2)
        
        axes[1,1].set_title('Evolução do Capital Total')
        axes[1,1].set_ylabel('Capital (R$)')
        axes[1,1].axhline(y=capital_inicial, color='black', linestyle='--', alpha=0.5, label='Capital Inicial')
        axes[1,1].legend()
        axes[1,1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # Salvar gráfico
    nome_arquivo = 'results/figures/comparacao_estrategias_investimento.png'
    os.makedirs(os.path.dirname(nome_arquivo), exist_ok=True)
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"   ✅ Gráfico salvo: {nome_arquivo}")

def executar_cenario_bootstrap(acoes_sorteadas, cenario_num):
    """
    Executa um cenário bootstrap com as ações sorteadas

    Args:
        acoes_sorteadas: Lista de ações sorteadas para este cenário
        cenario_num: Número do cenário

    Returns:
        Dicionário com resultados das 3 metodologias
    """
    print(f"\n🎲 CENÁRIO {cenario_num}")
    print("=" * 40)

    # Extrair apenas os tickers das ações sorteadas
    tickers_filtro = [ticker for ticker, _ in acoes_sorteadas]

    # Carregar dados das 3 metodologias filtrados pelas ações sorteadas
    dados_mm = carregar_dados_mm(tickers_filtro)
    dados_butterworth = carregar_dados_butterworth(tickers_filtro)
    dados_xgboost = carregar_dados_xgboost(tickers_filtro)

    # Verificar quais metodologias têm dados
    dados_disponiveis = []
    if dados_mm is not None:
        dados_disponiveis.append(('MM', dados_mm))
    if dados_butterworth is not None:
        dados_disponiveis.append(('Butterworth', dados_butterworth))
    if dados_xgboost is not None:
        dados_disponiveis.append(('XGBoost', dados_xgboost))

    if not dados_disponiveis:
        print(f"❌ Cenário {cenario_num}: Nenhum dado encontrado")
        return None

    # Simular investimento para cada metodologia
    resultados_cenario = {}

    for metodologia, dados in dados_disponiveis:
        try:
            # Filtrar para período configurado
            dados_periodo, _, _ = filtrar_periodo_simulacao(dados)

            if len(dados_periodo) > 0:
                # Simular investimento
                resultado = simular_investimento(dados_periodo, metodologia)
                resultados_cenario[metodologia] = resultado['rendimento_percentual']
            else:
                print(f"⚠️ Cenário {cenario_num}: Dados insuficientes para {metodologia}")
                resultados_cenario[metodologia] = 0.0

        except Exception as e:
            print(f"❌ Cenário {cenario_num}: Erro ao processar {metodologia}: {e}")
            resultados_cenario[metodologia] = 0.0

    return resultados_cenario

def executar_bootstrap():
    """
    Executa análise bootstrap com múltiplos cenários

    Returns:
        Dicionário com estatísticas (min, max, mediana) para cada metodologia
    """
    # Carregar configurações do bootstrap
    config_loader = ConfigLoader()
    bootstrap_config = config_loader.get('simulation.comparison.bootstrap', {})

    num_scenarios = bootstrap_config.get('num_scenarios', 10)
    stocks_per_scenario = bootstrap_config.get('stocks_per_scenario', 10)
    base_seed = bootstrap_config.get('random_seed', 42)

    print(f"\n🔄 ANÁLISE BOOTSTRAP")
    print("=" * 60)
    print(f"📊 Cenários: {num_scenarios}")
    print(f"📈 Ações por cenário: {stocks_per_scenario}")
    print(f"🎲 Seed base: {base_seed}")

    # Carregar todas as ações disponíveis
    acoes_disponiveis = carregar_acoes_disponiveis()

    if len(acoes_disponiveis) < stocks_per_scenario:
        print(f"❌ Erro: Apenas {len(acoes_disponiveis)} ações disponíveis, mas {stocks_per_scenario} necessárias por cenário")
        return None

    # Executar cenários
    resultados_todos_cenarios = {
        'MM': [],
        'Butterworth': [],
        'XGBoost': []
    }

    # Armazenar resultados detalhados por cenário
    resultados_detalhados = {}

    for cenario in range(1, num_scenarios + 1):
        # Usar seed diferente para cada cenário
        seed_cenario = base_seed + cenario

        # Sortear ações para este cenário
        acoes_sorteadas = sortear_acoes_cenario(acoes_disponiveis, stocks_per_scenario, seed_cenario)

        # Executar cenário
        resultado_cenario = executar_cenario_bootstrap(acoes_sorteadas, cenario)

        if resultado_cenario:
            # Armazenar resultados detalhados
            resultados_detalhados[cenario] = resultado_cenario

            # Armazenar resultados para estatísticas
            for metodologia in ['MM', 'Butterworth', 'XGBoost']:
                if metodologia in resultado_cenario:
                    resultados_todos_cenarios[metodologia].append(resultado_cenario[metodologia])

    # Calcular estatísticas
    estatisticas = {}

    for metodologia in ['MM', 'Butterworth', 'XGBoost']:
        rendimentos = resultados_todos_cenarios[metodologia]

        if rendimentos:
            estatisticas[metodologia] = {
                'rendimentos': rendimentos,
                'minimo': min(rendimentos),
                'maximo': max(rendimentos),
                'mediana': np.median(rendimentos),
                'media': np.mean(rendimentos),
                'desvio_padrao': np.std(rendimentos),
                'num_cenarios': len(rendimentos)
            }
        else:
            estatisticas[metodologia] = {
                'rendimentos': [],
                'minimo': 0.0,
                'maximo': 0.0,
                'mediana': 0.0,
                'media': 0.0,
                'desvio_padrao': 0.0,
                'num_cenarios': 0
            }

    return estatisticas, resultados_detalhados

def salvar_resultados_bootstrap_csv(estatisticas, resultados_detalhados):
    """
    Salva os resultados do bootstrap em arquivos CSV
    """
    print(f"\n💾 Salvando resultados em CSV...")

    # Criar diretório se não existir
    csv_dir = 'results/csv/bootstrap_analysis'
    os.makedirs(csv_dir, exist_ok=True)

    # 1. Salvar estatísticas resumidas
    resumo_data = []
    for metodologia in ['MM', 'Butterworth', 'XGBoost']:
        stats = estatisticas[metodologia]
        resumo_data.append({
            'Metodologia': metodologia,
            'Num_Cenarios': stats['num_cenarios'],
            'Rendimento_Minimo': stats['minimo'],
            'Rendimento_Mediana': stats['mediana'],
            'Rendimento_Maximo': stats['maximo'],
            'Rendimento_Media': stats['media'],
            'Desvio_Padrao': stats['desvio_padrao']
        })

    df_resumo = pd.DataFrame(resumo_data)
    arquivo_resumo = os.path.join(csv_dir, 'bootstrap_resumo_estatisticas.csv')
    df_resumo.to_csv(arquivo_resumo, index=False)
    print(f"   ✅ Estatísticas resumidas: {arquivo_resumo}")

    # 2. Salvar resultados detalhados por cenário
    detalhes_data = []
    for cenario, resultados in resultados_detalhados.items():
        for metodologia, rendimento in resultados.items():
            detalhes_data.append({
                'Cenario': cenario,
                'Metodologia': metodologia,
                'Rendimento_Percentual': rendimento
            })

    df_detalhes = pd.DataFrame(detalhes_data)
    arquivo_detalhes = os.path.join(csv_dir, 'bootstrap_resultados_detalhados.csv')
    df_detalhes.to_csv(arquivo_detalhes, index=False)
    print(f"   ✅ Resultados detalhados: {arquivo_detalhes}")

    # 3. Salvar matriz de rendimentos (cenários x metodologias)
    matriz_data = []
    for cenario in range(1, 11):  # 10 cenários
        linha = {'Cenario': cenario}
        if cenario in resultados_detalhados:
            for metodologia in ['MM', 'Butterworth', 'XGBoost']:
                linha[metodologia] = resultados_detalhados[cenario].get(metodologia, None)
        else:
            for metodologia in ['MM', 'Butterworth', 'XGBoost']:
                linha[metodologia] = None
        matriz_data.append(linha)

    df_matriz = pd.DataFrame(matriz_data)
    arquivo_matriz = os.path.join(csv_dir, 'bootstrap_matriz_rendimentos.csv')
    df_matriz.to_csv(arquivo_matriz, index=False)
    print(f"   ✅ Matriz de rendimentos: {arquivo_matriz}")

def criar_graficos_bootstrap(estatisticas):
    """
    Cria gráficos dos resultados do bootstrap
    """
    print(f"\n📊 Criando gráficos do bootstrap...")

    # Configurar estilo
    plt.style.use('default')

    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Análise Bootstrap - Comparação de Estratégias de Investimento', fontsize=16, fontweight='bold')

    metodologias = ['MM', 'Butterworth', 'XGBoost']
    cores = ['blue', 'green', 'red']

    # 1. Mínimo, Máximo e Mediana
    minimos = [estatisticas[m]['minimo'] for m in metodologias]
    maximos = [estatisticas[m]['maximo'] for m in metodologias]
    medianas = [estatisticas[m]['mediana'] for m in metodologias]

    x = np.arange(len(metodologias))
    width = 0.25

    axes[0,0].bar(x - width, minimos, width, label='Mínimo', color='red', alpha=0.7)
    axes[0,0].bar(x, medianas, width, label='Mediana', color='orange', alpha=0.7)
    axes[0,0].bar(x + width, maximos, width, label='Máximo', color='green', alpha=0.7)

    axes[0,0].set_title('Rendimento: Mínimo, Mediana e Máximo')
    axes[0,0].set_ylabel('Rendimento (%)')
    axes[0,0].set_xticks(x)
    axes[0,0].set_xticklabels(metodologias)
    axes[0,0].legend()
    axes[0,0].axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # Adicionar valores nas barras
    for i, (min_val, med_val, max_val) in enumerate(zip(minimos, medianas, maximos)):
        axes[0,0].text(i - width, min_val + (1 if min_val >= 0 else -3), f'{min_val:.1f}%',
                      ha='center', va='bottom' if min_val >= 0 else 'top', fontsize=8)
        axes[0,0].text(i, med_val + (1 if med_val >= 0 else -3), f'{med_val:.1f}%',
                      ha='center', va='bottom' if med_val >= 0 else 'top', fontsize=8)
        axes[0,0].text(i + width, max_val + (1 if max_val >= 0 else -3), f'{max_val:.1f}%',
                      ha='center', va='bottom' if max_val >= 0 else 'top', fontsize=8)

    # 2. Box plot dos rendimentos
    rendimentos_plot = []
    labels_plot = []

    for metodologia in metodologias:
        if estatisticas[metodologia]['rendimentos']:
            rendimentos_plot.append(estatisticas[metodologia]['rendimentos'])
            labels_plot.append(metodologia)

    if rendimentos_plot:
        axes[0,1].boxplot(rendimentos_plot, labels=labels_plot)
        axes[0,1].set_title('Distribuição dos Rendimentos')
        axes[0,1].set_ylabel('Rendimento (%)')
        axes[0,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # 3. Média e Desvio Padrão
    medias = [estatisticas[m]['media'] for m in metodologias]
    desvios = [estatisticas[m]['desvio_padrao'] for m in metodologias]

    axes[1,0].bar(metodologias, medias, color=cores, alpha=0.7, yerr=desvios, capsize=5)
    axes[1,0].set_title('Rendimento Médio ± Desvio Padrão')
    axes[1,0].set_ylabel('Rendimento (%)')
    axes[1,0].axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # Adicionar valores nas barras
    for i, (media, desvio) in enumerate(zip(medias, desvios)):
        axes[1,0].text(i, media + desvio + 1, f'{media:.1f}%±{desvio:.1f}%',
                      ha='center', va='bottom', fontweight='bold')

    # 4. Número de cenários válidos
    num_cenarios = [estatisticas[m]['num_cenarios'] for m in metodologias]

    bars4 = axes[1,1].bar(metodologias, num_cenarios, color=cores, alpha=0.7)
    axes[1,1].set_title('Cenários com Dados Válidos')
    axes[1,1].set_ylabel('Número de Cenários')

    # Adicionar valores nas barras
    for bar, valor in zip(bars4, num_cenarios):
        axes[1,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                      f'{valor}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()

    # Salvar gráfico
    nome_arquivo = 'results/figures/bootstrap_comparacao_estrategias.png'
    os.makedirs(os.path.dirname(nome_arquivo), exist_ok=True)
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"   ✅ Gráfico bootstrap salvo: {nome_arquivo}")

def main():
    """
    Função principal - executa análise bootstrap
    """
    # Configurar ambiente
    setup_environment()

    # Carregar configurações
    config_loader = ConfigLoader()
    capital_inicial = config_loader.get_initial_capital('comparison')
    periodo = config_loader.get_simulation_period('comparison')
    bootstrap_config = config_loader.get('simulation.comparison.bootstrap', {})

    num_scenarios = bootstrap_config.get('num_scenarios', 10)
    stocks_per_scenario = bootstrap_config.get('stocks_per_scenario', 10)

    print("💰 COMPARAÇÃO DE ESTRATÉGIAS DE INVESTIMENTO - MÉTODO BOOTSTRAP")
    print("=" * 80)
    print(f"📊 Simulação: R$ {capital_inicial:.0f} iniciais por {periodo}")
    print(f"🔍 Metodologias: MM, Butterworth, XGBoost")
    print(f"🎲 Bootstrap: {num_scenarios} cenários com {stocks_per_scenario} ações cada")
    print("=" * 80)

    # Executar análise bootstrap
    resultado_bootstrap = executar_bootstrap()

    if resultado_bootstrap is None:
        print("❌ Erro na execução do bootstrap")
        return

    estatisticas, resultados_detalhados = resultado_bootstrap

    # Salvar resultados em CSV
    salvar_resultados_bootstrap_csv(estatisticas, resultados_detalhados)

    # Criar gráficos do bootstrap
    criar_graficos_bootstrap(estatisticas)

    # Mostrar resumo final
    print(f"\n📋 RESUMO FINAL DO BOOTSTRAP")
    print("=" * 80)

    # Encontrar melhor metodologia baseada na mediana
    melhor_metodologia = max(estatisticas.keys(),
                           key=lambda x: estatisticas[x]['mediana'] if estatisticas[x]['num_cenarios'] > 0 else -float('inf'))

    for metodologia in ['MM', 'Butterworth', 'XGBoost']:
        stats = estatisticas[metodologia]

        if stats['num_cenarios'] > 0:
            status = "🏆" if metodologia == melhor_metodologia else "📊"
            print(f"\n{status} {metodologia} ({stats['num_cenarios']} cenários):")
            print(f"   📈 Rendimento Mínimo: {stats['minimo']:.2f}%")
            print(f"   📊 Rendimento Mediana: {stats['mediana']:.2f}%")
            print(f"   � Rendimento Máximo: {stats['maximo']:.2f}%")
            print(f"   � Rendimento Médio: {stats['media']:.2f}% ± {stats['desvio_padrao']:.2f}%")
        else:
            print(f"\n❌ {metodologia}: Nenhum cenário válido")

    if estatisticas[melhor_metodologia]['num_cenarios'] > 0:
        print(f"\n🏆 MELHOR ESTRATÉGIA (por mediana): {melhor_metodologia}")
        print(f"📈 Mediana de Rendimento: {estatisticas[melhor_metodologia]['mediana']:.2f}%")
        print(f"� Faixa de Rendimento: {estatisticas[melhor_metodologia]['minimo']:.2f}% a {estatisticas[melhor_metodologia]['maximo']:.2f}%")
    else:
        print("❌ Nenhuma metodologia teve cenários válidos")

if __name__ == "__main__":
    main()
