#!/usr/bin/env python3
"""
Módulo para carregar e gerenciar configurações centralizadas
Carrega parâmetros do arquivo config.yaml para uso em todos os scripts
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional

class ConfigLoader:
    """
    Classe para carregar e gerenciar configurações centralizadas
    """
    
    def __init__(self, config_file: str = 'config.yaml'):
        """
        Inicializa o carregador de configurações
        
        Args:
            config_file: Caminho para o arquivo de configuração YAML
        """
        self.config_file = config_file
        self.config = None
        self._load_config()
    
    def _load_config(self):
        """
        Carrega o arquivo de configuração YAML
        """
        # Procurar o arquivo de configuração no diretório raiz do projeto
        current_dir = Path(__file__).parent
        config_path = current_dir.parent / self.config_file
        
        if not config_path.exists():
            # Tentar no diretório atual
            config_path = Path(self.config_file)
            
        if not config_path.exists():
            raise FileNotFoundError(f"Arquivo de configuração não encontrado: {self.config_file}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
            print(f"✅ Configuração carregada de: {config_path}")
        except Exception as e:
            raise Exception(f"Erro ao carregar configuração: {e}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Obtém um valor da configuração usando notação de ponto
        
        Args:
            key_path: Caminho da chave usando notação de ponto (ex: 'data.periods.default_period')
            default: Valor padrão se a chave não for encontrada
            
        Returns:
            Valor da configuração ou valor padrão
        """
        if self.config is None:
            return default
        
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Obtém uma seção completa da configuração
        
        Args:
            section: Nome da seção
            
        Returns:
            Dicionário com a seção ou dicionário vazio
        """
        return self.config.get(section, {}) if self.config else {}
    
    def get_moving_average_windows(self) -> Dict[str, int]:
        """
        Obtém as janelas das médias móveis
        
        Returns:
            Dicionário com as janelas das médias móveis
        """
        return self.get_section('moving_averages').get('windows', {
            'mm10': 10, 'mm25': 25, 'mm50': 50, 'mm100': 100, 'mm200': 200
        })
    
    def get_lstm_config(self) -> Dict[str, Any]:
        """
        Obtém configurações do LSTM
        
        Returns:
            Dicionário com configurações do LSTM
        """
        return self.get_section('lstm')
    
    def get_kalman_config(self) -> Dict[str, Any]:
        """
        Obtém configurações do Kalman
        
        Returns:
            Dicionário com configurações do Kalman
        """
        return self.get_section('kalman')
    
    def get_butterworth_config(self) -> Dict[str, Any]:
        """
        Obtém configurações do Butterworth
        
        Returns:
            Dicionário com configurações do Butterworth
        """
        return self.get_section('butterworth')
    
    def get_visualization_config(self) -> Dict[str, Any]:
        """
        Obtém configurações de visualização
        
        Returns:
            Dicionário com configurações de visualização
        """
        return self.get_section('visualization')
    
    def get_data_config(self) -> Dict[str, Any]:
        """
        Obtém configurações de dados
        
        Returns:
            Dicionário com configurações de dados
        """
        return self.get_section('data')
    
    def get_output_directories(self) -> Dict[str, str]:
        """
        Obtém diretórios de saída
        
        Returns:
            Dicionário com diretórios de saída
        """
        return self.get('output.directories', {})
    
    def get_file_paths(self) -> Dict[str, str]:
        """
        Obtém caminhos dos arquivos de entrada
        
        Returns:
            Dicionário com caminhos dos arquivos
        """
        return self.get('data.files', {})
    
    def get_colors(self) -> Dict[str, str]:
        """
        Obtém configurações de cores para gráficos
        
        Returns:
            Dicionário com cores
        """
        return self.get('visualization.colors', {})
    
    def get_line_widths(self) -> Dict[str, float]:
        """
        Obtém configurações de espessura de linha
        
        Returns:
            Dicionário com espessuras de linha
        """
        return self.get('visualization.line_widths', {})
    
    def get_trading_strategy_config(self) -> Dict[str, Any]:
        """
        Obtém configurações da estratégia de trading

        Returns:
            Dicionário com configurações de trading
        """
        return self.get_section('trading_strategy')

    def get_simulation_config(self) -> Dict[str, Any]:
        """
        Obtém configurações de simulação

        Returns:
            Dicionário com configurações de simulação
        """
        return self.get_section('simulation')

    def get_initial_capital(self, context: str = 'default') -> float:
        """
        Obtém capital inicial baseado no contexto

        Args:
            context: Contexto da simulação ('default', 'comparison', 'portfolio_analysis')

        Returns:
            Valor do capital inicial
        """
        if context == 'comparison':
            return self.get('simulation.comparison.initial_capital', 1000.0)
        elif context == 'portfolio_analysis':
            return self.get('simulation.portfolio_analysis.initial_capital', 1000.0)
        else:
            return self.get('simulation.initial_capital', 1000.0)

    def get_simulation_period(self, context: str = 'default') -> str:
        """
        Obtém período de simulação baseado no contexto

        Args:
            context: Contexto da simulação ('default', 'comparison')

        Returns:
            Período de simulação (ex: '1y')
        """
        if context == 'comparison':
            return self.get('simulation.comparison.simulation_period', '1y')
        else:
            return self.get('simulation.simulation_period', '1y')
    
    def get_correlation_config(self) -> Dict[str, Any]:
        """
        Obtém configurações de análise de correlação
        
        Returns:
            Dicionário com configurações de correlação
        """
        return self.get_section('correlation')
    
    def get_api_config(self) -> Dict[str, Any]:
        """
        Obtém configurações de API
        
        Returns:
            Dicionário com configurações de API
        """
        return self.get_section('api')
    
    def get_spread_config(self) -> Dict[str, Any]:
        """
        Obtém configurações de spread
        
        Returns:
            Dicionário com configurações de spread
        """
        return self.get_section('spread')
    
    def create_output_directories(self):
        """
        Cria os diretórios de saída se não existirem
        """
        directories = self.get_output_directories()
        for dir_path in directories.values():
            os.makedirs(dir_path, exist_ok=True)
    
    def setup_matplotlib(self):
        """
        Configura matplotlib com base nas configurações
        """
        import matplotlib
        import matplotlib.pyplot as plt
        
        # Configurar backend
        backend = self.get('general.matplotlib_backend', 'Agg')
        matplotlib.use(backend)
        
        # Configurar estilo
        style = self.get('general.matplotlib_style', 'default')
        plt.style.use(style)
    
    def setup_warnings(self):
        """
        Configura warnings com base nas configurações
        """
        if self.get('general.suppress_warnings', True):
            import warnings
            warnings.filterwarnings('ignore')
    
    def setup_tensorflow(self):
        """
        Configura TensorFlow com base nas configurações
        """
        if self.get('performance.tensorflow_memory_growth', True):
            try:
                import tensorflow as tf
                gpus = tf.config.experimental.list_physical_devices('GPU')
                if gpus:
                    for gpu in gpus:
                        tf.config.experimental.set_memory_growth(gpu, True)
            except ImportError:
                pass  # TensorFlow não instalado
    
    def setup_environment(self):
        """
        Configura o ambiente completo com base nas configurações
        """
        self.setup_matplotlib()
        self.setup_warnings()
        self.setup_tensorflow()
        self.create_output_directories()


# Instância global do carregador de configurações
config = ConfigLoader()

# Funções de conveniência para acesso rápido às configurações
def get_config(key_path: str, default: Any = None) -> Any:
    """Função de conveniência para obter configurações"""
    return config.get(key_path, default)

def get_moving_average_windows() -> Dict[str, int]:
    """Função de conveniência para obter janelas das médias móveis"""
    return config.get_moving_average_windows()

def get_colors() -> Dict[str, str]:
    """Função de conveniência para obter cores"""
    return config.get_colors()

def get_file_paths() -> Dict[str, str]:
    """Função de conveniência para obter caminhos de arquivos"""
    return config.get_file_paths()

def setup_environment():
    """Função de conveniência para configurar o ambiente"""
    config.setup_environment()
