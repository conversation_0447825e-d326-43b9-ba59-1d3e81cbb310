#!/usr/bin/env python3
"""
Sistema de Cache Unificado - Todas as ações em um único arquivo
Atualização em lote para máxima eficiência
"""

import os
import sys
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datetime import datetime, timedelta
import yfinance as yf
from pathlib import Path
import json
from typing import List, Dict, Tuple
import time

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

class UnifiedCache:
    """
    Sistema de cache unificado - todas as ações em um arquivo único
    Inclui cache de features calculadas para acelerar processamento
    """

    def __init__(self):
        self.cache_dir = Path('data/cache/unified')
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Arquivos principais
        self.unified_file = self.cache_dir / 'all_stocks_data.parquet'
        self.metadata_file = self.cache_dir / 'unified_metadata.json'

        # Configurações
        self.compression = 'snappy'

        # Features que são calculadas e podem ser cacheadas
        # REMOVIDAS features do dia atual que usam OHLCV: Media_OHLC_PctChange, Volatilidade, Spread
        # REMOVIDAS features econométricas do dia atual que usam OHLCV
        self.cacheable_features = [
            # Features básicas (apenas Media_OHLC para referência)
            'Media_OHLC',
            # REMOVIDAS: 'Media_OHLC_PctChange', 'Volatilidade', 'Spread' (usam OHLCV do dia atual)
            # REMOVIDAS: Features econométricas atuais que usam OHLCV do dia atual
            # 'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA', 'Amihud', 'Roll_Spread',
            # 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
            # Features econométricas históricas (mantidas para lags)
            'MFI_Historical', 'Amihud_Historical', 'Roll_Spread_Historical',
            'Hurst_Historical', 'Vol_per_Volume_Historical', 'CMF_Historical', 'AD_Line_Historical',
            # Features temporais (mantidas)
            'Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta',
            'Mes_1', 'Mes_2', 'Mes_3', 'Mes_4', 'Mes_5', 'Mes_6',
            'Mes_7', 'Mes_8', 'Mes_9', 'Mes_10', 'Mes_11', 'Mes_12',
            'Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter',
            'Pre_Feriado_Brasil',
            # Sinais (mantidos)
            'Media_OHLC_Futura', 'Sinal_Compra', 'Sinal_Venda'
        ]

        # Adicionar features lagged dinamicamente baseado na configuração
        self._add_lagged_features()

    def _add_lagged_features(self):
        """
        Adiciona features lagged à lista de features cacheáveis baseado na configuração
        """
        try:
            # Obter configurações de lags
            ohlc_lags = config.get('xgboost.features.ohlc_lags', 5)
            econometric_lags = config.get('xgboost.features.econometric_lags', 3)

            # Adicionar lags da Media_OHLC_PctChange
            for i in range(1, ohlc_lags + 1):
                self.cacheable_features.append(f'Media_OHLC_PctChange_Lag_{i}')

            # Features econométricas que podem ter lags
            econometric_features_all = [
                'Volume', 'Spread', 'Volatilidade',
                'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
                'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
            ]

            # Adicionar lags das features econométricas
            for feature in econometric_features_all:
                for i in range(1, econometric_lags + 1):
                    self.cacheable_features.append(f'{feature}_Lag_{i}')

        except Exception as e:
            print(f"     ⚠️ Erro ao configurar features lagged: {e}")

    def _load_metadata(self) -> dict:
        """Carrega metadados do cache unificado"""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r') as f:
                return json.load(f)
        return {
            'last_update': None,
            'tickers': {},
            'total_records': 0,
            'date_range': {'start': None, 'end': None}
        }
    
    def _save_metadata(self, metadata: dict):
        """Salva metadados do cache unificado"""
        with open(self.metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
    
    def load_unified_data(self) -> Tuple[pd.DataFrame, dict]:
        """
        Carrega dados unificados do cache
        
        Returns:
            tuple: (dataframe_unificado, metadados)
        """
        if not self.unified_file.exists():
            return None, {}
        
        try:
            # Ler dados do Parquet
            data = pd.read_parquet(self.unified_file)
            metadata = self._load_metadata()

            # Verificar features cacheadas (considerar prefixos dos tickers)
            features_cacheadas = []
            for col in data.columns:
                # Remover prefixo do ticker (formato: TICKER_FEATURE)
                if '_' in col:
                    feature_name = '_'.join(col.split('_')[1:])  # Remove primeiro elemento (ticker)
                    if feature_name in self.cacheable_features:
                        features_cacheadas.append(col)

            has_features = len(features_cacheadas) > 0

            file_size = self.unified_file.stat().st_size
            if has_features:
                print(f"     📁 Cache unificado encontrado - {len(data)} registros, {len(features_cacheadas)} features ({file_size/1024/1024:.1f}MB)")
            else:
                print(f"     📁 Cache unificado encontrado - {len(data)} registros, sem features ({file_size/1024/1024:.1f}MB)")

            return data, metadata
            
        except Exception as e:
            print(f"     ⚠️ Erro ao ler cache unificado: {e}")
            return None, {}
    
    def save_unified_data(self, data: pd.DataFrame, tickers_info: Dict[str, dict]):
        """
        Salva dados unificados no cache
        
        Args:
            data: DataFrame com dados de todas as ações
            tickers_info: Informações sobre cada ticker
        """
        try:
            # Salvar em formato Parquet com compressão
            data.to_parquet(
                self.unified_file,
                compression=self.compression,
                index=True
            )
            
            # Contar features cacheadas (considerar prefixos dos tickers)
            features_cacheadas = []
            for col in data.columns:
                # Remover prefixo do ticker (formato: TICKER_FEATURE)
                if '_' in col:
                    feature_name = '_'.join(col.split('_')[1:])  # Remove primeiro elemento (ticker)
                    if feature_name in self.cacheable_features:
                        features_cacheadas.append(col)

            # Atualizar metadados
            metadata = {
                'last_update': datetime.now(),
                'tickers': tickers_info,
                'total_records': len(data),
                'date_range': {
                    'start': data.index.min(),
                    'end': data.index.max()
                },
                'compression': self.compression,
                'columns': list(data.columns),
                'features_cached': len(features_cacheadas),
                'has_features': len(features_cacheadas) > 0,
                'cached_features_list': features_cacheadas
            }
            self._save_metadata(metadata)
            
            file_size = self.unified_file.stat().st_size
            print(f"     💾 Cache unificado salvo - {len(data)} registros ({file_size/1024/1024:.1f}MB)")
            
        except Exception as e:
            print(f"     ❌ Erro ao salvar cache unificado: {e}")
    
    def batch_download_recent_data(self, tickers: List[str]) -> Dict[str, pd.DataFrame]:
        """
        Baixa dados recentes para múltiplas ações em lote
        MUITO mais eficiente que downloads individuais
        
        Args:
            tickers: Lista de tickers para baixar
            
        Returns:
            Dict com dados de cada ticker
        """
        print(f"     🚀 Download em lote para {len(tickers)} ações...")
        
        # ESTRATÉGIA: Cache até ANTEONTEM, sempre baixar HOJE e ONTEM frescos
        hoje = datetime.now().date()
        ontem = hoje - timedelta(days=1)
        anteontem = hoje - timedelta(days=2)
        data_inicio = anteontem  # Baixar desde anteontem (inclui anteontem, ontem e hoje)

        print(f"     ⚡ SEMPRE baixando dados frescos: ANTEONTEM ({anteontem}), ONTEM ({ontem}), HOJE ({hoje})")
        
        # Download em lote usando yfinance (muito mais eficiente)
        start_time = time.time()
        
        try:
            # yfinance pode baixar múltiplas ações de uma vez
            tickers_str = ' '.join(tickers)
            dados_lote = yf.download(tickers_str, start=data_inicio, progress=False, group_by='ticker')
            
            download_time = time.time() - start_time
            print(f"     ⚡ Download em lote concluído em {download_time:.2f}s")
            
            # Processar dados por ticker
            dados_por_ticker = {}
            
            if len(tickers) == 1:
                # Caso especial: apenas um ticker
                ticker = tickers[0]
                if dados_lote is not None and len(dados_lote) > 0:
                    dados_por_ticker[ticker] = dados_lote
            else:
                # Múltiplos tickers
                for ticker in tickers:
                    try:
                        if ticker in dados_lote.columns.levels[0]:
                            ticker_data = dados_lote[ticker]
                            if len(ticker_data.dropna()) > 0:
                                dados_por_ticker[ticker] = ticker_data
                    except Exception as e:
                        print(f"     ⚠️ Erro ao processar {ticker}: {e}")
            
            print(f"     ✅ Processados {len(dados_por_ticker)}/{len(tickers)} tickers com sucesso")
            return dados_por_ticker
            
        except Exception as e:
            print(f"     ❌ Erro no download em lote: {e}")
            return {}
    
    def _calculate_required_days(self, period_str: str) -> int:
        """
        Calcula quantos dias são necessários baseado no período configurado

        Args:
            period_str: String do período (ex: '15y', '5y', '2y', '1y', '6mo')

        Returns:
            Número mínimo de dias necessários
        """
        period_str = period_str.lower()

        if period_str.endswith('y'):
            years = int(period_str[:-1])
            return years * 365  # Aproximadamente
        elif period_str.endswith('mo'):
            months = int(period_str[:-2])
            return months * 30  # Aproximadamente
        elif period_str.endswith('d'):
            return int(period_str[:-1])
        else:
            # Default para casos não reconhecidos
            return 365 * 5  # 5 anos

    def _validate_cache_coverage(self, cached_data: pd.DataFrame, required_period: str) -> bool:
        """
        Valida se o cache tem cobertura suficiente para o período requerido
        E se os dados estão atualizados (últimos dias)

        Args:
            cached_data: Dados do cache
            required_period: Período requerido (ex: '15y')

        Returns:
            True se o cache tem dados suficientes E atualizados, False caso contrário
        """
        if cached_data is None or len(cached_data) == 0:
            return False

        # 1. Verificar cobertura de período
        required_days = self._calculate_required_days(required_period)
        actual_days = len(cached_data)

        # Considerar válido se temos pelo menos 60% dos dias necessários
        # (para compensar fins de semana, feriados, ações mais novas, etc.)
        min_acceptable_days = int(required_days * 0.6)

        period_valid = actual_days >= min_acceptable_days

        if not period_valid:
            print(f"     ⚠️ Cache insuficiente: {actual_days} dias < {min_acceptable_days} dias mínimos (período: {required_period})")
            return False

        # 2. NOVA ESTRATÉGIA: Cache válido até ANTEONTEM, sempre baixar HOJE e ONTEM
        hoje = datetime.now().date()
        anteontem = hoje - timedelta(days=2)
        ultima_data_cache = cached_data.index.max().date()

        # Cache é válido se tem dados pelo menos até anteontem
        # Isso força sempre o download de HOJE e ONTEM
        if ultima_data_cache >= anteontem:
            print(f"     ✅ Cache válido: {actual_days} dias >= {min_acceptable_days} dias mínimos")
            print(f"     📅 Cache até: {ultima_data_cache}, anteontem: {anteontem}")
            print(f"     ⚡ SEMPRE baixará dados frescos de HOJE e ONTEM")
            return True
        else:
            print(f"     ⚠️ Cache desatualizado: última data {ultima_data_cache}, anteontem é {anteontem}")
            print(f"     🔄 Precisa baixar dados históricos + HOJE e ONTEM")
            return False

    def update_unified_cache_with_features(self, all_tickers: List[Tuple], tickers_carteira=None) -> Dict[str, pd.DataFrame]:
        """
        Atualiza cache unificado com features calculadas
        Se há cache de features, só recalcula features dos últimos 2 dias

        Args:
            all_tickers: Lista de tuplas (ticker, nome, origem)
            tickers_carteira: Set de tickers da carteira para cálculo de sinais

        Returns:
            Dict com dados de cada ticker já com features calculadas
        """
        print("🔄 Atualizando cache unificado com features...")

        # Carregar cache existente
        cached_data, metadata = self.load_unified_data()

        # Extrair apenas os tickers
        tickers = [ticker for ticker in [item[0] for item in all_tickers]]

        # Obter período configurado
        periodo_config = config.get('xgboost.data_period', '15y')

        # Validar se o cache tem cobertura suficiente
        cache_valid = self._validate_cache_coverage(cached_data, periodo_config)

        # Verificar se há features no cache (verificar diretamente no DataFrame)
        features_cacheadas = []
        if cached_data is not None:
            for col in cached_data.columns:
                # Remover prefixo do ticker (formato: TICKER_FEATURE)
                if '_' in col:
                    feature_name = '_'.join(col.split('_')[1:])  # Remove primeiro elemento (ticker)
                    if feature_name in self.cacheable_features:
                        features_cacheadas.append(col)

        has_features = len(features_cacheadas) > 0

        if cached_data is not None and len(cached_data) > 0 and cache_valid:
            # PRIMEIRO: Sempre atualizar cache com dados de HOJE e ONTEM
            print("     🔄 Atualizando cache com dados de HOJE e ONTEM...")
            cached_data_atualizado = self.update_unified_cache(all_tickers)

            if cached_data_atualizado is not None and len(cached_data_atualizado) > 0:
                if has_features:
                    print("     ⚡ Features encontradas no cache - recalculando apenas últimos 2 dias")
                    return self._update_features_incrementally(cached_data_atualizado, metadata, all_tickers, tickers_carteira)
                else:
                    print("     🔧 Sem features no cache - calculando features completas")
                    return self._calculate_all_features(cached_data_atualizado, all_tickers, tickers_carteira)
            else:
                # Fallback para dados originais se atualização falhar
                if has_features:
                    print("     ⚠️ Falha na atualização - usando cache original")
                    return self._update_features_incrementally(cached_data, metadata, all_tickers, tickers_carteira)
                else:
                    return self._calculate_all_features(cached_data, all_tickers, tickers_carteira)
        else:
            # Não há cache válido, baixar dados completos e calcular features
            print("     📊 Cache inválido ou inexistente - download completo e cálculo de features")
            unified_data = self.update_unified_cache(all_tickers)
            if unified_data is not None:
                return self._calculate_all_features(unified_data, all_tickers, tickers_carteira)
            else:
                return {}

    def update_unified_cache(self, all_tickers: List[Tuple[str, str]]) -> pd.DataFrame:
        """
        Atualiza cache unificado com dados recentes
        Faz download em lote e atualiza tudo de uma vez

        Args:
            all_tickers: Lista de tuplas (ticker, nome)

        Returns:
            DataFrame unificado atualizado
        """
        print("🔄 Atualizando cache unificado...")
        
        # Carregar cache existente
        cached_data, metadata = self.load_unified_data()

        # Extrair apenas os tickers (all_tickers pode ter 2 ou 3 elementos por tupla)
        tickers = [ticker for ticker in [item[0] for item in all_tickers]]

        # Obter período configurado
        periodo_config = config.get('xgboost.data_period', '15y')

        # Validar se o cache tem cobertura suficiente
        cache_valid = self._validate_cache_coverage(cached_data, periodo_config)

        if cached_data is not None and len(cached_data) > 0 and cache_valid:
            # Cache existe - fazer download em lote dos dados recentes
            print("     📊 Cache existente encontrado - fazendo atualização incremental")
            
            # Download em lote dos dados recentes
            dados_recentes = self.batch_download_recent_data(tickers)
            
            if dados_recentes:
                # Combinar dados existentes com dados recentes
                print("     🔄 Combinando dados históricos com dados recentes...")
                
                # Determinar data de corte para evitar duplicatas
                hoje = datetime.now().date()
                data_corte = hoje - timedelta(days=3)
                data_corte_ts = pd.Timestamp(data_corte)
                
                # Estratégia: manter todos os dados históricos e apenas atualizar os últimos dias
                dados_combinados = cached_data.copy()

                for ticker in tickers:
                    if ticker in dados_recentes:
                        ticker_recente = dados_recentes[ticker]

                        # Adicionar prefixo do ticker às colunas
                        ticker_recente.columns = [f"{ticker}_{col}" for col in ticker_recente.columns]

                        # Atualizar apenas as datas dos dados recentes
                        for col in ticker_recente.columns:
                            if col in dados_combinados.columns:
                                # Separar índices existentes e novos
                                existing_idx = ticker_recente.index.intersection(dados_combinados.index)
                                new_idx = ticker_recente.index.difference(dados_combinados.index)

                                # Atualizar valores existentes
                                if len(existing_idx) > 0:
                                    dados_combinados.loc[existing_idx, col] = ticker_recente.loc[existing_idx, col]

                                # Adicionar novos valores
                                if len(new_idx) > 0:
                                    # Criar DataFrame temporário para os novos dados
                                    new_data = pd.DataFrame(index=new_idx, columns=dados_combinados.columns)
                                    new_data[col] = ticker_recente.loc[new_idx, col]
                                    # Concatenar com dados existentes
                                    dados_combinados = pd.concat([dados_combinados, new_data])
                            else:
                                # Adicionar nova coluna se não existir
                                # Primeiro, garantir que todos os índices existam
                                new_idx = ticker_recente.index.difference(dados_combinados.index)
                                if len(new_idx) > 0:
                                    # Criar DataFrame temporário para os novos índices
                                    new_data = pd.DataFrame(index=new_idx, columns=dados_combinados.columns)
                                    dados_combinados = pd.concat([dados_combinados, new_data])

                                # Agora adicionar a nova coluna
                                dados_combinados[col] = ticker_recente[col]
                
                # Remover duplicatas e ordenar
                dados_combinados = dados_combinados.loc[~dados_combinados.index.duplicated(keep='last')]
                dados_combinados = dados_combinados.sort_index()
                
                # Atualizar informações dos tickers
                tickers_info = {}
                for item in all_tickers:
                    ticker = item[0]
                    nome = item[1] if len(item) > 1 else ticker
                    tickers_info[ticker] = {
                        'name': nome,
                        'columns': [col for col in dados_combinados.columns if col.startswith(f"{ticker}_")],
                        'last_date': dados_combinados.index.max(),
                        'total_records': len(dados_combinados)
                    }
                
                # Salvar cache atualizado
                self.save_unified_data(dados_combinados, tickers_info)
                
                print(f"     ✅ Cache unificado atualizado - {len(dados_combinados)} registros")
                return dados_combinados
            else:
                print("     ⚠️ Falha no download em lote, usando cache existente")
                return cached_data
        
        else:
            # Não há cache - criar do zero com dados completos
            print("     📊 Criando cache unificado do zero...")
            
            periodo = config.get('xgboost.data_period', '15y')
            print(f"     📅 Baixando {periodo} de dados para {len(tickers)} ações...")
            
            # Download em lote de dados completos
            start_time = time.time()
            
            try:
                tickers_str = ' '.join(tickers)
                dados_completos = yf.download(tickers_str, period=periodo, progress=False, group_by='ticker')
                
                download_time = time.time() - start_time
                print(f"     ⚡ Download completo em {download_time:.2f}s")
                
                if dados_completos is not None and len(dados_completos) > 0:
                    # Processar dados para formato unificado
                    if len(tickers) == 1:
                        # Caso especial: apenas um ticker
                        ticker = tickers[0]
                        dados_completos.columns = [f"{ticker}_{col}" for col in dados_completos.columns]
                        dados_unificados = dados_completos
                    else:
                        # Múltiplos tickers - dados já vêm com MultiIndex
                        # Achatar MultiIndex para formato ticker_coluna
                        dados_unificados = pd.DataFrame(index=dados_completos.index)
                        
                        for ticker in tickers:
                            if ticker in dados_completos.columns.levels[0]:
                                ticker_data = dados_completos[ticker]
                                for col in ticker_data.columns:
                                    dados_unificados[f"{ticker}_{col}"] = ticker_data[col]
                    
                    # Criar informações dos tickers
                    tickers_info = {}
                    for item in all_tickers:
                        ticker = item[0]
                        nome = item[1] if len(item) > 1 else ticker
                        ticker_cols = [col for col in dados_unificados.columns if col.startswith(f"{ticker}_")]
                        if ticker_cols:
                            tickers_info[ticker] = {
                                'name': nome,
                                'columns': ticker_cols,
                                'last_date': dados_unificados.index.max(),
                                'total_records': len(dados_unificados)
                            }
                    
                    # Salvar cache inicial
                    self.save_unified_data(dados_unificados, tickers_info)
                    
                    print(f"     ✅ Cache unificado criado - {len(dados_unificados)} registros")
                    return dados_unificados
                
            except Exception as e:
                print(f"     ❌ Erro no download completo: {e}")
                return None
        
        return None
    
    def _validate_ticker_data(self, ticker_data: pd.DataFrame, ticker: str, required_period: str) -> bool:
        """
        Valida se os dados de um ticker específico são suficientes

        Args:
            ticker_data: Dados do ticker
            ticker: Nome do ticker
            required_period: Período requerido

        Returns:
            True se os dados são suficientes, False caso contrário
        """
        if ticker_data is None or len(ticker_data) == 0:
            return False

        required_days = self._calculate_required_days(required_period)
        actual_days = len(ticker_data)

        # Considerar válido se temos pelo menos 60% dos dias necessários
        min_acceptable_days = int(required_days * 0.6)

        is_valid = actual_days >= min_acceptable_days

        if not is_valid:
            print(f"     ⚠️ {ticker} dados insuficientes: {actual_days} dias < {min_acceptable_days} dias mínimos")

        return is_valid

    def get_ticker_data(self, unified_data: pd.DataFrame, ticker: str) -> pd.DataFrame:
        """
        Extrai dados de um ticker específico do DataFrame unificado
        
        Args:
            unified_data: DataFrame unificado
            ticker: Ticker desejado
            
        Returns:
            DataFrame com dados do ticker (colunas OHLCV padrão)
        """
        # Encontrar colunas do ticker
        ticker_cols = [col for col in unified_data.columns if col.startswith(f"{ticker}_")]
        
        if not ticker_cols:
            return None
        
        # Extrair dados do ticker
        ticker_data = unified_data[ticker_cols].copy()
        
        # Renomear colunas removendo o prefixo do ticker
        ticker_data.columns = [col.replace(f"{ticker}_", "") for col in ticker_data.columns]
        
        # Remover linhas com todos os valores NaN
        ticker_data = ticker_data.dropna(how='all')

        # Validar se os dados são suficientes baseado no período configurado
        periodo_config = config.get('xgboost.data_period', '15y')
        if not self._validate_ticker_data(ticker_data, ticker, periodo_config):
            print(f"     🔄 {ticker} precisa de download completo devido a dados insuficientes")
            return None  # Retorna None para indicar que precisa de download completo

        return ticker_data
    
    def get_cache_stats(self) -> dict:
        """Retorna estatísticas do cache unificado"""
        if not self.unified_file.exists():
            return {'exists': False}
        
        metadata = self._load_metadata()
        file_size = self.unified_file.stat().st_size
        
        return {
            'exists': True,
            'file_size': file_size,
            'file_size_mb': file_size / (1024 * 1024),
            'total_records': metadata.get('total_records', 0),
            'total_tickers': len(metadata.get('tickers', {})),
            'last_update': metadata.get('last_update'),
            'date_range': metadata.get('date_range', {}),
            'compression': metadata.get('compression', 'unknown')
        }

    def _calculate_all_features(self, unified_data: pd.DataFrame, all_tickers: List[Tuple], tickers_carteira=None) -> Dict[str, pd.DataFrame]:
        """
        Calcula features completas para todos os tickers
        """
        try:
            # Importar função de cálculo de features
            from classificador_xgboost_sinais import calcular_features_e_sinais

            acoes_dados = {}

            # Extrair dados de cada ticker e calcular features
            for ticker, nome, origem in all_tickers:
                ticker_data = self.get_ticker_data(unified_data, ticker)
                if ticker_data is not None and len(ticker_data) > 0:
                    print(f"     🔧 Calculando features completas para {ticker}")
                    dados_com_features = calcular_features_e_sinais(ticker_data, ticker, tickers_carteira)
                    acoes_dados[ticker] = dados_com_features

            # Salvar dados com features de volta no cache unificado
            if acoes_dados:
                self._save_features_to_unified_cache(acoes_dados, all_tickers)

            return acoes_dados

        except Exception as e:
            print(f"     ⚠️ Erro ao calcular features completas: {e}")
            return {}

    def _update_features_incrementally(self, cached_data: pd.DataFrame, metadata: dict,
                                     all_tickers: List[Tuple], tickers_carteira=None) -> Dict[str, pd.DataFrame]:
        """
        Atualiza features apenas para os últimos 2 dias
        """
        try:
            # Importar função de cálculo de features
            from classificador_xgboost_sinais import calcular_features_e_sinais

            # Determinar data de corte para recálculo (últimos 2 dias)
            hoje = datetime.now().date()
            data_corte = hoje - timedelta(days=2)
            data_corte_ts = pd.Timestamp(data_corte)

            acoes_dados = {}

            # Para cada ticker, extrair dados e recalcular features incrementalmente
            for ticker, nome, origem in all_tickers:
                ticker_data = self.get_ticker_data(cached_data, ticker)
                if ticker_data is not None and len(ticker_data) > 0:

                    # Separar dados históricos (com features) dos dados recentes (sem features atualizadas)
                    dados_historicos = ticker_data[ticker_data.index < data_corte_ts].copy()
                    dados_recentes_raw = ticker_data[ticker_data.index >= data_corte_ts].copy()

                    if len(dados_recentes_raw) == 0:
                        # Não há dados novos, usar cache como está
                        acoes_dados[ticker] = ticker_data
                        continue

                    # Para calcular features dos dados recentes, precisamos de contexto histórico
                    janela_contexto = 100  # Dias de contexto para cálculos rolling
                    data_contexto = data_corte_ts - timedelta(days=janela_contexto)

                    # Dados para cálculo (contexto + recentes)
                    dados_para_calculo = ticker_data[ticker_data.index >= data_contexto].copy()

                    # Remover features antigas dos dados para cálculo (manter apenas OHLCV)
                    colunas_ohlcv = ['Open', 'High', 'Low', 'Close', 'Volume']
                    colunas_para_manter = [col for col in colunas_ohlcv if col in dados_para_calculo.columns]
                    dados_para_calculo = dados_para_calculo[colunas_para_manter]

                    print(f"     ⚡ Recalculando features incrementais para {ticker} - {len(dados_recentes_raw)} dias")

                    # Calcular features para toda a janela (contexto + recentes)
                    dados_com_features_janela = calcular_features_e_sinais(dados_para_calculo, ticker, tickers_carteira)

                    # Extrair apenas as features dos dados recentes
                    dados_recentes_com_features = dados_com_features_janela[dados_com_features_janela.index >= data_corte_ts]

                    # Combinar dados históricos (com features do cache) + dados recentes (com features recalculadas)
                    resultado = pd.concat([dados_historicos, dados_recentes_com_features])

                    # Remover duplicatas e ordenar
                    resultado = resultado[~resultado.index.duplicated(keep='last')]
                    resultado = resultado.sort_index()

                    acoes_dados[ticker] = resultado

            # Salvar dados atualizados de volta no cache unificado
            if acoes_dados:
                self._save_features_to_unified_cache(acoes_dados, all_tickers)

            return acoes_dados

        except Exception as e:
            print(f"     ⚠️ Erro no cálculo incremental: {e}")
            print(f"     🔄 Fallback para cálculo completo")
            return self._calculate_all_features(cached_data, all_tickers, tickers_carteira)

    def _save_features_to_unified_cache(self, acoes_dados: Dict[str, pd.DataFrame], all_tickers: List[Tuple]):
        """
        Salva dados com features de volta no cache unificado
        """
        try:
            # Converter dados individuais de volta para formato unificado
            unified_data_list = []
            tickers_info = {}

            for ticker, nome, origem in all_tickers:
                if ticker in acoes_dados:
                    dados = acoes_dados[ticker]

                    # Adicionar prefixo do ticker às colunas
                    dados_prefixed = dados.copy()
                    dados_prefixed.columns = [f"{ticker}_{col}" for col in dados.columns]

                    unified_data_list.append(dados_prefixed)

                    # Informações do ticker
                    tickers_info[ticker] = {
                        'nome': nome,
                        'origem': origem,
                        'total_days': len(dados),
                        'data_start': dados.index.min(),
                        'data_end': dados.index.max()
                    }

            if unified_data_list:
                # Combinar todos os dados
                unified_data = pd.concat(unified_data_list, axis=1, sort=True)

                # Salvar no cache
                self.save_unified_data(unified_data, tickers_info)
                print(f"     💾 Features salvas no cache unificado - {len(acoes_dados)} ações")

        except Exception as e:
            print(f"     ⚠️ Erro ao salvar features no cache: {e}")

    def _consolidate_all_data_to_unified_cache(self, acoes_dados: Dict[str, pd.DataFrame], all_tickers: List[Tuple]):
        """
        Consolida todas as ações (cache + individuais) no cache unificado
        """
        try:
            print(f"     🔄 Consolidando {len(acoes_dados)} ações no cache unificado...")

            # Converter dados individuais para formato unificado
            unified_data_list = []
            tickers_info = {}

            for ticker, nome, origem in all_tickers:
                if ticker in acoes_dados:
                    dados = acoes_dados[ticker]

                    # Adicionar prefixo do ticker às colunas
                    dados_prefixed = dados.copy()
                    dados_prefixed.columns = [f"{ticker}_{col}" for col in dados.columns]

                    unified_data_list.append(dados_prefixed)

                    # Informações do ticker
                    tickers_info[ticker] = {
                        'nome': nome,
                        'origem': origem,
                        'total_days': len(dados),
                        'data_start': dados.index.min(),
                        'data_end': dados.index.max()
                    }

            if unified_data_list:
                # Combinar todos os dados
                unified_data = pd.concat(unified_data_list, axis=1, sort=True)

                # Salvar no cache unificado
                self.save_unified_data(unified_data, tickers_info)
                print(f"     💾 Cache unificado consolidado - {len(acoes_dados)} ações, {len(unified_data)} registros")

        except Exception as e:
            print(f"     ⚠️ Erro na consolidação do cache unificado: {e}")

    def clear_cache(self):
        """Remove cache unificado"""
        if self.unified_file.exists():
            self.unified_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()
        print("🗑️ Cache unificado limpo")

# Instância global do cache unificado
unified_cache = UnifiedCache()
