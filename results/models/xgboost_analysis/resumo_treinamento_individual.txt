RESUMO DO TREINAMENTO XGBOOST INDIVIDUAL - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-09 12:20:49

MODELOS INDIVIDUAIS:
  • Tipo: XGBoost Multiclasse (um modelo por ação)
  • Classes: 0=Sem Ação, 1=Compra, 2=Venda
  • Função de perda: Cross-entropy (mlogloss)
  • Features: pct_change da média OHLC (variação percentual)
  • Total de modelos treinados: 6
  • Acurácia média: 0.497
  • Acurácia mín/máx: 0.446 / 0.558

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 5y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (13):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade

RESULTADOS POR AÇÃO:
  • BBSE3: Acurácia = 0.482 (treino: 983, teste: 249)
  • BEEF3: Acurácia = 0.446 (treino: 983, teste: 249)
  • DEXP4: Acurácia = 0.474 (treino: 927, teste: 249)
  • KLBN3: Acurácia = 0.498 (treino: 983, teste: 249)
  • SAPR3: Acurácia = 0.526 (treino: 983, teste: 249)
  • VVEO3: Acurácia = 0.558 (treino: 716, teste: 249)

ESTATÍSTICAS CONSOLIDADAS:
  • Acurácia Média: 0.497
  • Desvio Padrão: 0.036
  • Acurácia Mínima: 0.446
  • Acurácia Máxima: 0.558

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
