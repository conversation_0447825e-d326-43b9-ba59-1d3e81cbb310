ANÁLISE BOOTSTRAP - COMPARAÇÃO DE ESTRATÉGIAS DE INVESTIMENTO
================================================================

CONFIGURAÇÃO DA ANÁLISE:
- Número de cenários: 10
- Ações por cenário: 10 (sorteadas aleatoriamente)
- Capital inicial: R$ 1.000,00
- Período de simulação: 1 ano
- Total de ações disponíveis: 32

METODOLOGIA:
Para cada cenário, foram sorteadas 10 ações entre as 32 disponíveis no arquivo
de diversificação. Cada metodologia (MM, Butterworth, XGBoost) foi testada
com o mesmo conjunto de ações sorteadas, permitindo comparação justa.

RESULTADOS CONSOLIDADOS:
================================================================

📊 MÉDIAS MÓVEIS (MM) - 10 cenários válidos:
   • Rendimento Mínimo: -27.19%
   • Rendimento Mediana: 5.80%
   • Rendimento Máximo: 76.28%
   • Rendimento Médio: 8.82% ± 26.24%

📊 FILTRO BUTTERWORTH - 10 cenários válidos:
   • Rendimento Mínimo: -24.43%
   • Rendimento Mediana: 6.31%
   • Rendimento Máximo: 71.44%
   • Rendimento Médio: 12.08% ± 28.19%

🏆 XGBOOST - 10 cenários válidos:
   • Rendimento Mínimo: 100.97%
   • Rendimento Mediana: 161.08%
   • Rendimento Máximo: 224.49%
   • Rendimento Médio: 160.04% ± 38.83%

ANÁLISE COMPARATIVA:
================================================================

MELHOR ESTRATÉGIA: XGBoost
- Mediana de rendimento: 161.08%
- Faixa de rendimento: 100.97% a 224.49%
- Consistentemente superior em todos os cenários

DESEMPENHO RELATIVO:
1. XGBoost: Claramente superior, com rendimentos sempre positivos e 
   significativamente maiores que as outras metodologias
2. Butterworth: Ligeiramente superior ao MM em mediana (6.31% vs 5.80%)
3. MM: Desempenho mais conservador, com maior variabilidade

OBSERVAÇÕES IMPORTANTES:
- XGBoost nunca teve rendimento negativo (mínimo: 100.97%)
- MM e Butterworth tiveram cenários com perdas (até -27.19% e -24.43%)
- A variabilidade do XGBoost é maior (±38.83%) mas em patamares superiores
- Todas as metodologias conseguiram processar os 10 cenários

CONCLUSÕES:
================================================================

1. SUPERIORIDADE DO XGBOOST: O método XGBoost demonstrou superioridade
   consistente e significativa sobre as metodologias tradicionais.

2. ROBUSTEZ: O XGBoost mostrou-se robusto, mantendo rendimentos positivos
   em todos os cenários testados.

3. RISCO vs RETORNO: Embora o XGBoost tenha maior variabilidade, seus
   retornos são substancialmente superiores, justificando o risco adicional.

4. METODOLOGIAS TRADICIONAIS: MM e Butterworth apresentaram desempenhos
   similares, com o Butterworth ligeiramente superior.

5. DIVERSIFICAÇÃO: A análise bootstrap com diferentes combinações de ações
   confirma a consistência dos resultados across diferentes portfolios.

RECOMENDAÇÃO:
Com base nos resultados do bootstrap, recomenda-se o uso da metodologia
XGBoost para estratégias de investimento, dado seu desempenho superior
consistente em todos os cenários testados.
