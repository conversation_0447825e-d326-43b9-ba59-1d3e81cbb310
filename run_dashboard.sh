#!/bin/bash

# Script para executar o Dashboard da Carteira de Investimentos
# Uso: ./run_dashboard.sh

echo "📈 Dashboard - Análise Temporal da Carteira de Investimentos"
echo "============================================================"

# Verificar se o arquivo de dados existe
if [ ! -f "results/evolucao_carteira_temporal.csv" ]; then
    echo "⚠️  Dados da evolução temporal não encontrados."
    echo "🔄 Executando análise temporal primeiro..."
    uv run python src/analise_carteira_temporal.py
    
    if [ $? -ne 0 ]; then
        echo "❌ Erro ao executar análise temporal."
        exit 1
    fi
    echo "✅ Análise temporal concluída!"
fi

echo "🚀 Iniciando dashboard..."
echo "📱 O dashboard será aberto em: http://localhost:8501"
echo "⏹️  Para parar o dashboard, pressione Ctrl+C"
echo ""

# Executar o dashboard
uv run streamlit run dashboard_carteira.py
